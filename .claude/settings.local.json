{"permissions": {"allow": ["WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(mkdir:*)", "Bash(rm -rf:*)", "<PERSON><PERSON>(mv:*)", "Bash(npm install:*)", "Bash(npm run build:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(docker run:*)", "Bash(npx tailwindcss init:*)", "Bash(docker-compose -f docker-compose.prod.yml up --build -d)", "Bash(docker info:*)", "<PERSON>sh(docker-compose -f docker-compose.prod.yml up -d)", "Bash(docker-compose -f docker-compose.prod.yml up -d database cache)", "Bash(docker-compose -f docker-compose.prod.yml up -d cache)", "Bash(docker-compose -f docker-compose.prod.yml up -d database)", "<PERSON><PERSON>(echo:*)", "Bash(brew services list:*)", "<PERSON><PERSON>(mysql:*)", "<PERSON><PERSON>(curl:*)", "Bash(lsof:*)", "Bash(brew install:*)", "Bash(ss:*)", "Bash(sudo lsof:*)", "<PERSON><PERSON>(pkill:*)", "Bash(grep:*)", "Bash(cp:*)", "<PERSON><PERSON>(open:*)"], "deny": [], "ask": [], "additionalDirectories": ["/Users/<USER>/develop/apache-maven-3.9.4-bin/apache-maven-3.9.4/conf"]}}