# Dependencies
node_modules/
frontend/node_modules/
/.pnp
.pnp.js

# Testing
/coverage
backend/target/

# Production
/build
frontend/dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
backend/logs/
*.log

# IDE
.idea/
.vscode/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Java
*.class
*.jar
!backend/src/main/resources/
target/

# Maven
.mvn/
mvnw
mvnw.cmd

# Gradle
.gradle/
build/

# Database
*.db
*.sqlite

# Temporary files
*.tmp
*.temp