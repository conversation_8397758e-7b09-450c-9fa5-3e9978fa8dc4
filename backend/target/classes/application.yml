server:
  port: 3000

spring:
  application:
    name: myproject-backend
  datasource:
    url: **************************************************************************************************
    username: root
    password: Lly214412!
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
  redis:
    host: localhost
    port: 6379
  mail:
    host: smtp.gmail.com
    port: 587
    username: <EMAIL>
    password: bogghfskegecdjef
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

logging:
  level:
    com.myproject: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/application.log

jwt:
  secret: mySecretKeyThatIsAtLeast32BytesLongForHS256Algorithm
  expiration: 86400000

# Actuator endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info