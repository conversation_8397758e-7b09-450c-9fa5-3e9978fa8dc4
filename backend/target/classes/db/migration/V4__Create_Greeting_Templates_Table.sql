-- Greeting Templates Table
CREATE TABLE IF NOT EXISTS greeting_templates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    content TEXT NOT NULL,
    style_type VARCHAR(100),
    category VARCHAR(100),
    holiday_id BIGINT,
    is_active BOOLEAN DEFAULT TRUE,
    language VARCHAR(10) DEFAULT 'zh',
    created_by <PERSON><PERSON>IN<PERSON>,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIG<PERSON> KEY (holiday_id) REFERENCES holidays(id) ON DELETE SET NULL,
    FOREI<PERSON><PERSON> KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Indexes for better query performance
CREATE INDEX idx_greeting_templates_category ON greeting_templates(category);
CREATE INDEX idx_greeting_templates_style_type ON greeting_templates(style_type);
CREATE INDEX idx_greeting_templates_holiday_id ON greeting_templates(holiday_id);
CREATE INDEX idx_greeting_templates_language ON greeting_templates(language);
CREATE INDEX idx_greeting_templates_is_active ON greeting_templates(is_active);
CREATE INDEX idx_greeting_templates_created_by ON greeting_templates(created_by);