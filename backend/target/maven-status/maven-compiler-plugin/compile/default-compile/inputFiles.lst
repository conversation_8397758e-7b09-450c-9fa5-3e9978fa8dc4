/Users/<USER>/MyProject/backend/src/main/java/com/myproject/service/UserService.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/dto/FriendGroupRequest.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/entity/UserFriendGroup.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/dto/GreetingTemplateRequest.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/entity/UserRole.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/security/AuthEntryPointJwt.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/entity/Holiday.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/repository/UserRoleRepository.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/service/HolidayEmailService.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/repository/FriendRepository.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/security/UserDetailsImpl.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/dto/FriendRequest.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/security/UserDetailsServiceImpl.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/repository/FriendGroupRepository.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/repository/GreetingTemplateRepository.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/entity/Role.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/controller/AuthController.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/dto/HolidayResponse.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/entity/User.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/dto/JwtResponse.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/dto/FriendGroupResponse.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/MyProjectApplication.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/controller/GreetingTemplateController.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/dto/HolidayRequest.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/dto/MessageResponse.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/security/WebSecurityConfig.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/entity/Friend.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/security/JwtUtils.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/service/UserSettingsService.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/security/AuthTokenFilter.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/controller/FriendController.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/repository/UserFriendGroupRepository.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/service/HolidayService.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/service/FriendService.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/entity/FriendGroup.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/controller/HolidayController.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/dto/LoginRequest.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/controller/UserSettingsController.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/controller/UserController.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/controller/HealthController.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/dto/RegisterRequest.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/dto/FriendResponse.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/service/GreetingTemplateService.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/entity/GreetingTemplate.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/service/EmailService.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/repository/RoleRepository.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/repository/UserRepository.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/dto/GreetingTemplateResponse.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/repository/HolidayRepository.java
/Users/<USER>/MyProject/backend/src/main/java/com/myproject/dto/UserInfoResponse.java
