package com.myproject;

import com.myproject.entity.GreetingTemplate;
import com.myproject.repository.GreetingTemplateRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.time.LocalDateTime;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@SpringBootTest
public class GreetingTemplateRepositoryTest {

    @Autowired
    private GreetingTemplateRepository greetingTemplateRepository;

    @Test
    public void testCreateAndRetrieveGreetingTemplate() {
        // Create a new greeting template
        GreetingTemplate template = new GreetingTemplate();
        template.setName("春节祝福");
        template.setContent("亲爱的{name}，祝您春节快乐，万事如意！");
        template.setStyleType("formal");
        template.setCategory("holiday");
        template.setIsActive(true);
        template.setLanguage("zh");
        template.setCreatedBy(1L);
        template.setCreatedAt(LocalDateTime.now());
        template.setUpdatedAt(LocalDateTime.now());

        // Save the template
        GreetingTemplate savedTemplate = greetingTemplateRepository.save(template);

        // Retrieve the template
        GreetingTemplate retrievedTemplate = greetingTemplateRepository.findById(savedTemplate.getId()).orElse(null);

        // Verify the template was saved and retrieved correctly
        assertThat(retrievedTemplate).isNotNull();
        assertThat(retrievedTemplate.getName()).isEqualTo("春节祝福");
        assertThat(retrievedTemplate.getContent()).isEqualTo("亲爱的{name}，祝您春节快乐，万事如意！");
        assertThat(retrievedTemplate.getStyleType()).isEqualTo("formal");
        assertThat(retrievedTemplate.getCategory()).isEqualTo("holiday");
    }

    @Test
    public void testFindByCategory() {
        // Create a new greeting template
        GreetingTemplate template = new GreetingTemplate();
        template.setName("生日祝福");
        template.setContent("祝{name}生日快乐！");
        template.setStyleType("casual");
        template.setCategory("birthday");
        template.setIsActive(true);
        template.setLanguage("zh");
        template.setCreatedBy(1L);
        template.setCreatedAt(LocalDateTime.now());
        template.setUpdatedAt(LocalDateTime.now());

        // Save the template
        greetingTemplateRepository.save(template);

        // Find templates by category
        List<GreetingTemplate> birthdayTemplates = greetingTemplateRepository.findByCategoryAndIsActiveTrue("birthday");

        // Verify that we found at least one birthday template
        assertThat(birthdayTemplates).isNotEmpty();
        assertThat(birthdayTemplates.get(0).getCategory()).isEqualTo("birthday");
    }
}