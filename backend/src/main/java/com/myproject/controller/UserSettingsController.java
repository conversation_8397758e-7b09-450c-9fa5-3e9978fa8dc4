package com.myproject.controller;

import com.myproject.service.UserSettingsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalTime;

@RestController
@RequestMapping("/api/user-settings")
public class UserSettingsController {

    @Autowired
    private UserSettingsService userSettingsService;

    @PutMapping("/{userId}/reminder-time")
    public ResponseEntity<?> updateReminderTime(@PathVariable Long userId, @RequestBody LocalTime reminderTime) {
        try {
            userSettingsService.updateUserReminderTime(userId, reminderTime);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @GetMapping("/{userId}/reminder-time")
    public ResponseEntity<LocalTime> getReminderTime(@PathVariable Long userId) {
        try {
            LocalTime reminderTime = userSettingsService.getUserReminderTime(userId);
            return ResponseEntity.ok(reminderTime);
        } catch (Exception e) {
            return ResponseEntity.badRequest().build();
        }
    }
}