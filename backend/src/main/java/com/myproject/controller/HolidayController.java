package com.myproject.controller;

import com.myproject.dto.HolidayRequest;
import com.myproject.dto.HolidayResponse;
import com.myproject.service.HolidayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/holidays")
@CrossOrigin(origins = "*", maxAge = 3600)
public class HolidayController {

    @Autowired
    private HolidayService holidayService;

    // Create a new holiday
    @PostMapping
    public ResponseEntity<HolidayResponse> createHoliday(@RequestBody HolidayRequest holidayRequest) {
        HolidayResponse response = holidayService.createHoliday(holidayRequest);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    // Get all holidays
    @GetMapping
    public ResponseEntity<List<HolidayResponse>> getAllHolidays() {
        List<HolidayResponse> holidays = holidayService.getAllHolidays();
        return new ResponseEntity<>(holidays, HttpStatus.OK);
    }

    // Get holiday by ID
    @GetMapping("/{id}")
    public ResponseEntity<HolidayResponse> getHolidayById(@PathVariable Long id) {
        HolidayResponse holiday = holidayService.getHolidayById(id);
        return new ResponseEntity<>(holiday, HttpStatus.OK);
    }

    // Update holiday
    @PutMapping("/{id}")
    public ResponseEntity<HolidayResponse> updateHoliday(@PathVariable Long id, @RequestBody HolidayRequest holidayRequest) {
        HolidayResponse response = holidayService.updateHoliday(id, holidayRequest);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    // Delete holiday
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteHoliday(@PathVariable Long id) {
        holidayService.deleteHoliday(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    // Get holidays by date
    @GetMapping("/date/{date}")
    public ResponseEntity<List<HolidayResponse>> getHolidaysByDate(@PathVariable String date) {
        LocalDate localDate = LocalDate.parse(date);
        List<HolidayResponse> holidays = holidayService.getHolidaysByDate(localDate);
        return new ResponseEntity<>(holidays, HttpStatus.OK);
    }

    // Get holidays by type
    @GetMapping("/type/{type}")
    public ResponseEntity<List<HolidayResponse>> getHolidaysByType(@PathVariable String type) {
        List<HolidayResponse> holidays = holidayService.getHolidaysByType(type);
        return new ResponseEntity<>(holidays, HttpStatus.OK);
    }

    // Get holidays in date range
    @GetMapping("/range")
    public ResponseEntity<List<HolidayResponse>> getHolidaysInRange(
            @RequestParam String startDate,
            @RequestParam String endDate) {
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        List<HolidayResponse> holidays = holidayService.getHolidaysInRange(start, end);
        return new ResponseEntity<>(holidays, HttpStatus.OK);
    }

    // Search holidays by name
    @GetMapping("/search")
    public ResponseEntity<List<HolidayResponse>> searchHolidaysByName(@RequestParam String name) {
        List<HolidayResponse> holidays = holidayService.searchHolidaysByName(name);
        return new ResponseEntity<>(holidays, HttpStatus.OK);
    }

    // Get recurring holidays
    @GetMapping("/recurring")
    public ResponseEntity<List<HolidayResponse>> getRecurringHolidays() {
        List<HolidayResponse> holidays = holidayService.getRecurringHolidays();
        return new ResponseEntity<>(holidays, HttpStatus.OK);
    }
}