package com.myproject.controller;

import com.myproject.dto.FriendRequest;
import com.myproject.dto.FriendResponse;
import com.myproject.dto.FriendGroupRequest;
import com.myproject.dto.FriendGroupResponse;
import com.myproject.service.FriendService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/friends")
public class FriendController {

    @Autowired
    private FriendService friendService;

    // Friend endpoints

    @PostMapping
    public ResponseEntity<FriendResponse> addFriend(
            @RequestAttribute("userId") Long userId,
            @RequestBody FriendRequest friendRequest) {
        FriendResponse response = friendService.addFriend(userId, friendRequest);
        return ResponseEntity.ok(response);
    }

    @GetMapping
    public ResponseEntity<List<FriendResponse>> getUserFriends(
            @RequestAttribute("userId") Long userId) {
        List<FriendResponse> friends = friendService.getUserFriends(userId);
        return ResponseEntity.ok(friends);
    }

    @PutMapping("/{friendId}")
    public ResponseEntity<FriendResponse> updateFriend(
            @RequestAttribute("userId") Long userId,
            @PathVariable Long friendId,
            @RequestBody FriendRequest friendRequest) {
        FriendResponse response = friendService.updateFriend(userId, friendId, friendRequest);
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{friendId}")
    public ResponseEntity<Void> removeFriend(
            @RequestAttribute("userId") Long userId,
            @PathVariable Long friendId) {
        friendService.removeFriend(userId, friendId);
        return ResponseEntity.noContent().build();
    }

    // Friend Group endpoints

    @PostMapping("/groups")
    public ResponseEntity<FriendGroupResponse> createFriendGroup(
            @RequestAttribute("userId") Long userId,
            @RequestBody FriendGroupRequest groupRequest) {
        FriendGroupResponse response = friendService.createFriendGroup(userId, groupRequest);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/groups")
    public ResponseEntity<List<FriendGroupResponse>> getUserFriendGroups(
            @RequestAttribute("userId") Long userId) {
        List<FriendGroupResponse> groups = friendService.getUserFriendGroups(userId);
        return ResponseEntity.ok(groups);
    }

    @PutMapping("/groups/{groupId}")
    public ResponseEntity<FriendGroupResponse> updateFriendGroup(
            @RequestAttribute("userId") Long userId,
            @PathVariable Long groupId,
            @RequestBody FriendGroupRequest groupRequest) {
        FriendGroupResponse response = friendService.updateFriendGroup(userId, groupId, groupRequest);
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/groups/{groupId}")
    public ResponseEntity<Void> deleteFriendGroup(
            @RequestAttribute("userId") Long userId,
            @PathVariable Long groupId) {
        friendService.deleteFriendGroup(userId, groupId);
        return ResponseEntity.noContent().build();
    }
}