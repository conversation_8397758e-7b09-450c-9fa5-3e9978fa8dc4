package com.myproject.controller;

import com.myproject.dto.GreetingTemplateRequest;
import com.myproject.dto.GreetingTemplateResponse;
import com.myproject.service.GreetingTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@CrossOrigin(origins = "*", maxAge = 3600)
@RestController
@RequestMapping("/api/templates")
public class GreetingTemplateController {

    @Autowired
        private GreetingTemplateService greetingTemplateService;

    @PostMapping
    public ResponseEntity<GreetingTemplateResponse> createTemplate(@RequestBody GreetingTemplateRequest request) {
        // In a real application, you would get the user ID from the authentication context
        Long userId = 1L; // Placeholder for now
        GreetingTemplateResponse response = greetingTemplateService.createTemplate(request, userId);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public ResponseEntity<GreetingTemplateResponse> getTemplateById(@PathVariable Long id) {
        GreetingTemplateResponse response = greetingTemplateService.getTemplateById(id);
        return ResponseEntity.ok(response);
    }

    @GetMapping
    public ResponseEntity<List<GreetingTemplateResponse>> getAllTemplates() {
        List<GreetingTemplateResponse> templates = greetingTemplateService.getAllTemplates();
        return ResponseEntity.ok(templates);
    }

    @GetMapping("/active")
    public ResponseEntity<List<GreetingTemplateResponse>> getActiveTemplates() {
        List<GreetingTemplateResponse> templates = greetingTemplateService.getActiveTemplates();
        return ResponseEntity.ok(templates);
    }

    @GetMapping("/category/{category}")
    public ResponseEntity<List<GreetingTemplateResponse>> getTemplatesByCategory(@PathVariable String category) {
        List<GreetingTemplateResponse> templates = greetingTemplateService.getTemplatesByCategory(category);
        return ResponseEntity.ok(templates);
    }

    @GetMapping("/style/{styleType}")
    public ResponseEntity<List<GreetingTemplateResponse>> getTemplatesByStyleType(@PathVariable String styleType) {
        List<GreetingTemplateResponse> templates = greetingTemplateService.getTemplatesByStyleType(styleType);
        return ResponseEntity.ok(templates);
    }

    @GetMapping("/holiday/{holidayId}")
    public ResponseEntity<List<GreetingTemplateResponse>> getTemplatesByHolidayId(@PathVariable Long holidayId) {
        List<GreetingTemplateResponse> templates = greetingTemplateService.getTemplatesByHolidayId(holidayId);
        return ResponseEntity.ok(templates);
    }

    @GetMapping("/search")
    public ResponseEntity<List<GreetingTemplateResponse>> searchTemplates(@RequestParam String keyword) {
        List<GreetingTemplateResponse> templates = greetingTemplateService.searchTemplates(keyword);
        return ResponseEntity.ok(templates);
    }

    @PutMapping("/{id}")
    public ResponseEntity<GreetingTemplateResponse> updateTemplate(@PathVariable Long id, @RequestBody GreetingTemplateRequest request) {
        GreetingTemplateResponse response = greetingTemplateService.updateTemplate(id, request);
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTemplate(@PathVariable Long id) {
        greetingTemplateService.deleteTemplate(id);
        return ResponseEntity.ok().build();
    }
}