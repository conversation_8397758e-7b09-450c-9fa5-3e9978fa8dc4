package com.myproject.controller;

import com.myproject.entity.User;
import com.myproject.service.UserSettingsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalTime;

@RestController
@RequestMapping("/api/users")
@CrossOrigin(origins = "*", maxAge = 3600)
public class UserController {

    @Autowired
    private UserSettingsService userSettingsService;

    @PutMapping("/{id}/reminder-time")
    public ResponseEntity<User> updateReminderTime(@PathVariable Long id, @RequestBody LocalTime reminderTime) {
        User updatedUser = userSettingsService.updateUserReminderTime(id, reminderTime);
        return ResponseEntity.ok(updatedUser);
    }

    @GetMapping("/{id}/reminder-time")
    public ResponseEntity<LocalTime> getReminderTime(@PathVariable Long id) {
        LocalTime reminderTime = userSettingsService.getUserReminderTime(id);
        return ResponseEntity.ok(reminderTime);
    }
}