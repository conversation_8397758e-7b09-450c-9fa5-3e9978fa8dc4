package com.myproject.repository;

import com.myproject.entity.GreetingTemplate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface GreetingTemplateRepository extends JpaRepository<GreetingTemplate, Long> {
    
    List<GreetingTemplate> findByIsActiveTrue();
    
    List<GreetingTemplate> findByCategoryAndIsActiveTrue(String category);
    
    List<GreetingTemplate> findByStyleTypeAndIsActiveTrue(String styleType);
    
    List<GreetingTemplate> findByHolidayIdAndIsActiveTrue(Long holidayId);
    
    List<GreetingTemplate> findByLanguageAndIsActiveTrue(String language);
    
    @Query("SELECT gt FROM GreetingTemplate gt WHERE gt.category = :category AND gt.styleType = :styleType AND gt.isActive = true")
    List<GreetingTemplate> findByCategoryAndStyleType(@Param("category") String category, @Param("styleType") String styleType);
    
    @Query("SELECT gt FROM GreetingTemplate gt WHERE gt.name LIKE %:keyword% AND gt.isActive = true")
    List<GreetingTemplate> searchByKeyword(@Param("keyword") String keyword);
}