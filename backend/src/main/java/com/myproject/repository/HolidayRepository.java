package com.myproject.repository;

import com.myproject.entity.Holiday;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface HolidayRepository extends JpaRepository<Holiday, Long> {
    
    // Find holidays by date
    List<Holiday> findByDate(LocalDate date);
    
    // Find holidays by type
    List<Holiday> findByType(String type);
    
    // Find holidays within a date range
    @Query("SELECT h FROM Holiday h WHERE h.date BETWEEN :startDate AND :endDate")
    List<Holiday> findHolidaysInRange(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    // Find holidays by name (case insensitive)
    List<Holiday> findByNameContainingIgnoreCase(String name);
    
    // Check if a holiday exists on a specific date
    boolean existsByDate(LocalDate date);
    
    // Find recurring holidays
    List<Holiday> findByIsRecurringTrue();
}