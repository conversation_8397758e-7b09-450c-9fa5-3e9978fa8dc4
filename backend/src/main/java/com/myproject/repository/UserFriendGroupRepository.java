package com.myproject.repository;

import com.myproject.entity.UserFriendGroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserFriendGroupRepository extends JpaRepository<UserFriendGroup, Long> {
    
    List<UserFriendGroup> findByUserIdAndFriendId(Long userId, Long friendId);
    
    List<UserFriendGroup> findByUserIdAndGroupId(Long userId, Long groupId);
    
    List<UserFriendGroup> findByGroupId(Long groupId);
    
    void deleteByUserIdAndFriendIdAndGroupId(Long userId, Long friendId, Long groupId);
    
    @Query("SELECT ufg.groupId FROM UserFriendGroup ufg WHERE ufg.userId = :userId AND ufg.friendId = :friendId")
    List<Long> findGroupIdsByUserIdAndFriendId(@Param("userId") Long userId, @Param("friendId") Long friendId);
}