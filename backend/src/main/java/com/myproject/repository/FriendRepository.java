package com.myproject.repository;

import com.myproject.entity.Friend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FriendRepository extends JpaRepository<Friend, Long> {
    
    List<Friend> findByUserId(Long userId);
    
    List<Friend> findByFriendId(Long friendId);
    
    Optional<Friend> findByUserIdAndFriendId(Long userId, Long friendId);
    
    @Query("SELECT f FROM Friend f WHERE f.userId = :userId OR f.friendId = :userId")
    List<Friend> findAllUserFriends(@Param("userId") Long userId);
    
    void deleteByUserIdAndFriendId(Long userId, Long friendId);
}