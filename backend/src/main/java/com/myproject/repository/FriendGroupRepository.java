package com.myproject.repository;

import com.myproject.entity.FriendGroup;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface FriendGroupRepository extends JpaRepository<FriendGroup, Long> {
    
    List<FriendGroup> findByUserId(Long userId);
    
    List<FriendGroup> findByUserIdOrderBySortOrderAsc(Long userId);
}