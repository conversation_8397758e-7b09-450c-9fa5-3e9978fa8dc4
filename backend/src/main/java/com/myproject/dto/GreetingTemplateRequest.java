package com.myproject.dto;

import java.time.LocalDateTime;

public class GreetingTemplateRequest {
    private String name;
    private String content;
    private String styleType;
    private String category;
    private Long holidayId;
    private Boolean isActive;
    private String language;

    // Constructors
    public GreetingTemplateRequest() {}

    public GreetingTemplateRequest(String name, String content, String styleType, String category, 
                                  Long holidayId, Boolean isActive, String language) {
        this.name = name;
        this.content = content;
        this.styleType = styleType;
        this.category = category;
        this.holidayId = holidayId;
        this.isActive = isActive;
        this.language = language;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getStyleType() {
        return styleType;
    }

    public void setStyleType(String styleType) {
        this.styleType = styleType;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Long getHolidayId() {
        return holidayId;
    }

    public void setHolidayId(Long holidayId) {
        this.holidayId = holidayId;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }
}