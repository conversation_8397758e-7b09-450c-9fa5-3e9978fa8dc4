package com.myproject.dto;

import java.time.LocalDate;

public class HolidayRequest {
    private String name;
    private LocalDate date;
    private String type;
    private String description;
    private Boolean isRecurring;

    // Constructors
    public HolidayRequest() {}

    public HolidayRequest(String name, LocalDate date, String type, String description, Boolean isRecurring) {
        this.name = name;
        this.date = date;
        this.type = type;
        this.description = description;
        this.isRecurring = isRecurring;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getIsRecurring() {
        return isRecurring;
    }

    public void setIsRecurring(Boolean isRecurring) {
        this.isRecurring = isRecurring;
    }
}