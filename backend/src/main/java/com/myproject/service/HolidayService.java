package com.myproject.service;

import com.myproject.dto.HolidayRequest;
import com.myproject.dto.HolidayResponse;
import com.myproject.entity.Holiday;
import com.myproject.repository.HolidayRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class HolidayService {

    @Autowired
    private HolidayRepository holidayRepository;

    // Convert Holiday entity to HolidayResponse DTO
    private HolidayResponse convertToResponse(Holiday holiday) {
        return new HolidayResponse(
            holiday.getId(),
            holiday.getName(),
            holiday.getDate(),
            holiday.getType(),
            holiday.getDescription(),
            holiday.getIsRecurring(),
            holiday.getCreatedAt(),
            holiday.getUpdatedAt()
        );
    }

    // Convert HolidayRequest DTO to Holiday entity
    private Holiday convertToEntity(HolidayRequest holidayRequest) {
        return new Holiday(
            holidayRequest.getName(),
            holidayRequest.getDate(),
            holidayRequest.getType(),
            holidayRequest.getDescription(),
            holidayRequest.getIsRecurring()
        );
    }

    // Create a new holiday
    public HolidayResponse createHoliday(HolidayRequest holidayRequest) {
        Holiday holiday = convertToEntity(holidayRequest);
        Holiday savedHoliday = holidayRepository.save(holiday);
        return convertToResponse(savedHoliday);
    }

    // Get all holidays
    public List<HolidayResponse> getAllHolidays() {
        return holidayRepository.findAll().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    // Get holiday by ID
    public HolidayResponse getHolidayById(Long id) {
        Holiday holiday = holidayRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Holiday not found with id: " + id));
        return convertToResponse(holiday);
    }

    // Update holiday
    public HolidayResponse updateHoliday(Long id, HolidayRequest holidayRequest) {
        Holiday existingHoliday = holidayRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Holiday not found with id: " + id));
        
        existingHoliday.setName(holidayRequest.getName());
        existingHoliday.setDate(holidayRequest.getDate());
        existingHoliday.setType(holidayRequest.getType());
        existingHoliday.setDescription(holidayRequest.getDescription());
        existingHoliday.setIsRecurring(holidayRequest.getIsRecurring());
        
        Holiday updatedHoliday = holidayRepository.save(existingHoliday);
        return convertToResponse(updatedHoliday);
    }

    // Delete holiday
    public void deleteHoliday(Long id) {
        if (!holidayRepository.existsById(id)) {
            throw new RuntimeException("Holiday not found with id: " + id);
        }
        holidayRepository.deleteById(id);
    }

    // Get holidays by date
    public List<HolidayResponse> getHolidaysByDate(LocalDate date) {
        return holidayRepository.findByDate(date).stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    // Get holidays by type
    public List<HolidayResponse> getHolidaysByType(String type) {
        return holidayRepository.findByType(type).stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    // Get holidays in date range
    public List<HolidayResponse> getHolidaysInRange(LocalDate startDate, LocalDate endDate) {
        return holidayRepository.findHolidaysInRange(startDate, endDate).stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    // Search holidays by name
    public List<HolidayResponse> searchHolidaysByName(String name) {
        return holidayRepository.findByNameContainingIgnoreCase(name).stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    // Get recurring holidays
    public List<HolidayResponse> getRecurringHolidays() {
        return holidayRepository.findByIsRecurringTrue().stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }
}