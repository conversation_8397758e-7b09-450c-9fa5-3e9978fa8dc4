package com.myproject.service;

import com.myproject.entity.GreetingTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class EmailService {

    @Autowired
    private JavaMailSender mailSender;

    public void sendRegistrationEmail(String toEmail, String username) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(toEmail);
            message.setSubject("Welcome to MyProject - Registration Successful");
            message.setText("Dear " + username + ",\n\n" +
                    "Thank you for registering with MyProject! Your account has been successfully created.\n\n" +
                    "You can now log in to your account and start using our services.\n\n" +
                    "Best regards,\n" +
                    "The MyProject Team");
            
            mailSender.send(message);
        } catch (Exception e) {
            System.err.println("Failed to send registration email to " + toEmail + ": " + e.getMessage());
            throw e;
        }
    }

    public void sendHolidayReminderEmail(String toEmail, String username, String holidayName, String holidayDate, String greetingTemplate) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(toEmail);
            message.setSubject("Holiday Reminder - " + holidayName);
            
            String emailContent;
            if (greetingTemplate != null && !greetingTemplate.isEmpty()) {
                // Use the greeting template with placeholders
                emailContent = greetingTemplate
                    .replace("{username}", username != null ? username : "User")
                    .replace("{holidayName}", holidayName)
                    .replace("{holidayDate}", holidayDate);
            } else {
                // Use default message if no template is provided
                emailContent = "Dear " + (username != null ? username : "User") + ",\n\n" +
                    "This is a friendly reminder that " + holidayName + " is coming up on " + holidayDate + ".\n\n" +
                    "We hope you have a wonderful celebration!\n\n" +
                    "Best regards,\n" +
                    "The MyProject Team";
            }
            
            message.setText(emailContent);
            mailSender.send(message);
        } catch (Exception e) {
            System.err.println("Failed to send holiday reminder email to " + toEmail + ": " + e.getMessage());
            throw e;
        }
    }
    
    public void sendHolidayReminderEmail(String toEmail, String username, String holidayName, String holidayDate, List<GreetingTemplate> templates) {
        // If multiple templates are provided, we'll use the first active one
        String templateContent = null;
        if (templates != null && !templates.isEmpty()) {
            // Find the first active template
            for (GreetingTemplate template : templates) {
                if (template.getIsActive() == null || template.getIsActive()) {
                    templateContent = template.getContent();
                    break;
                }
            }
        }
        
        sendHolidayReminderEmail(toEmail, username, holidayName, holidayDate, templateContent);
    }
    
    public void sendHolidayReminderEmail(String toEmail, String holidayName, String holidayDate) {
        // Backward compatibility method
        sendHolidayReminderEmail(toEmail, null, holidayName, holidayDate, (String)null);
    }
}