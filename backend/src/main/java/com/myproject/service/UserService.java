package com.myproject.service;

import com.myproject.dto.LoginRequest;
import com.myproject.dto.RegisterRequest;
import com.myproject.entity.User;
import com.myproject.repository.UserRepository;
import com.myproject.service.EmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

@Service
public class UserService {
    @Autowired
    UserRepository userRepository;

    @Autowired
    PasswordEncoder encoder;

    @Autowired
    AuthenticationManager authenticationManager;

    @Autowired
    EmailService emailService;

    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    public User createUser(RegisterRequest signUpRequest) {
        // Create new user's account
        User user = new User(signUpRequest.getUsername(),
                signUpRequest.getEmail(),
                encoder.encode(signUpRequest.getPassword()));

        User savedUser = userRepository.save(user);
        
        // Send registration email
        try {
            emailService.sendRegistrationEmail(signUpRequest.getEmail(), signUpRequest.getUsername());
        } catch (Exception e) {
            // Log the error but don't fail the registration
            System.err.println("Failed to send registration email: " + e.getMessage());
        }
        
        return savedUser;
    }

    public Authentication authenticateUser(LoginRequest loginRequest) {
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(loginRequest.getEmail(), loginRequest.getPassword()));

        SecurityContextHolder.getContext().setAuthentication(authentication);
        return authentication;
    }
    
    public User getUserById(Long id) {
        return userRepository.findById(id).orElse(null);
    }
}