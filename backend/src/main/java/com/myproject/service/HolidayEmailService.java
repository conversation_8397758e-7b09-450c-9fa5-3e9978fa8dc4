package com.myproject.service;

import com.myproject.dto.HolidayResponse;
import com.myproject.dto.GreetingTemplateResponse;
import com.myproject.entity.GreetingTemplate;
import com.myproject.entity.User;
import com.myproject.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
public class HolidayEmailService {

    @Autowired
    private HolidayService holidayService;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private EmailService emailService;

    @Autowired
    private UserSettingsService userSettingsService;

    @Autowired
    private GreetingTemplateService greetingTemplateService;

    /**
     * Send holiday reminders based on user settings
     * Runs every hour to check if any users should receive reminders
     * Cron expression: second minute hour day-of-month month day-of-week
     * 0 0 * * * * = every hour
     */
    @Scheduled(cron = "0 0 * * * *")
    public void sendDailyHolidayReminders() {
        LocalDate today = LocalDate.now();
        LocalDate tomorrow = today.plusDays(1);
        LocalTime currentTime = LocalTime.now();
        
        // Get holidays for tomorrow
        List<HolidayResponse> upcomingHolidays = 
            holidayService.getHolidaysByDate(tomorrow);
        
        // Also get recurring holidays that match tomorrow's month and day
        List<HolidayResponse> recurringHolidays = 
            holidayService.getRecurringHolidays();
        
        // Filter recurring holidays to only those matching tomorrow's date
        recurringHolidays.removeIf(holiday -> 
            holiday.getDate().getMonthValue() != tomorrow.getMonthValue() || 
            holiday.getDate().getDayOfMonth() != tomorrow.getDayOfMonth());
        
        // Combine both lists
        upcomingHolidays.addAll(recurringHolidays);
        
        if (!upcomingHolidays.isEmpty()) {
            // Get all users
            List<User> users = userRepository.findAll();
            
            // Send reminder emails to users based on their settings
            for (User user : users) {
                // Check if user should receive reminder at this time
                if (userSettingsService.shouldUserReceiveReminder(user.getId(), currentTime)) {
                    for (HolidayResponse holiday : upcomingHolidays) {
                        String holidayDate = holiday.getDate().format(DateTimeFormatter.ofPattern("MMMM d, yyyy"));
                        
                        try {
                            // Get greeting templates for this holiday
List<GreetingTemplateResponse> templates = 
    greetingTemplateService.getTemplatesByHolidayId(holiday.getId());

// Convert to entities for email service (only active templates)
List<GreetingTemplate> templateEntities = templates.stream()
    .filter(template -> template.getIsActive() == null || template.getIsActive())
    .map(template -> {
        GreetingTemplate entity = new GreetingTemplate();
        entity.setId(template.getId());
        entity.setName(template.getName());
        entity.setContent(template.getContent());
        entity.setStyleType(template.getStyleType());
        entity.setCategory(template.getCategory());
        entity.setHolidayId(template.getHolidayId());
        entity.setIsActive(template.getIsActive());
        entity.setLanguage(template.getLanguage());
        entity.setCreatedBy(template.getCreatedBy());
        entity.setCreatedAt(template.getCreatedAt());
        entity.setUpdatedAt(template.getUpdatedAt());
        return entity;
    })
    .toList();

emailService.sendHolidayReminderEmail(
    user.getEmail(),
    user.getUsername(),
    holiday.getName(),
    holidayDate,
    templateEntities
);
                        } catch (Exception e) {
                            System.err.println("Failed to send holiday reminder email to " + 
                                user.getEmail() + ": " + e.getMessage());
                        }
                    }
                }
            }
        }
    }
}