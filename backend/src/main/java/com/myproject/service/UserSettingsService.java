package com.myproject.service;

import com.myproject.entity.User;
import com.myproject.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalTime;

@Service
public class UserSettingsService {

    @Autowired
    private UserRepository userRepository;

    public User updateUserReminderTime(Long userId, LocalTime reminderTime) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        
        user.setReminderTime(reminderTime);
        return userRepository.save(user);
    }

    public LocalTime getUserReminderTime(Long userId) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        
        return user.getReminderTime();
    }

    public boolean shouldUserReceiveReminder(Long userId, LocalTime currentTime) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with id: " + userId));
        
        LocalTime reminderTime = user.getReminderTime();
        
        // If user hasn't set a reminder time, use default (9 AM)
        if (reminderTime == null) {
            reminderTime = LocalTime.of(9, 0); // Default to 9 AM
        }
        
        // Check if current time matches the user's reminder time (within a 1-hour window)
        return currentTime.getHour() == reminderTime.getHour() && 
               currentTime.getMinute() >= reminderTime.getMinute() && 
               currentTime.getMinute() < reminderTime.getMinute() + 15;
    }
}