package com.myproject.service;

import com.myproject.dto.FriendRequest;
import com.myproject.dto.FriendResponse;
import com.myproject.dto.FriendGroupRequest;
import com.myproject.dto.FriendGroupResponse;
import com.myproject.entity.Friend;
import com.myproject.entity.FriendGroup;
import com.myproject.entity.User;
import com.myproject.entity.UserFriendGroup;
import com.myproject.repository.FriendRepository;
import com.myproject.repository.FriendGroupRepository;
import com.myproject.repository.UserFriendGroupRepository;
import com.myproject.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class FriendService {

    @Autowired
    private FriendRepository friendRepository;

    @Autowired
    private FriendGroupRepository friendGroupRepository;

    @Autowired
    private UserFriendGroupRepository userFriendGroupRepository;

    @Autowired
    private UserRepository userRepository;

    // Friend-related methods

    public FriendResponse addFriend(Long userId, FriendRequest friendRequest) {
        Friend friend = new Friend(userId, friendRequest.getFriendId());
        friend.setNickname(friendRequest.getNickname());
        friend.setPhone(friendRequest.getPhone());
        friend.setEmail(friendRequest.getEmail());
        friend.setAvatarUrl(friendRequest.getAvatarUrl());
        friend.setNotes(friendRequest.getNotes());
        friend.setIsFavorite(friendRequest.getIsFavorite());

        Friend savedFriend = friendRepository.save(friend);

        User friendUser = userRepository.findById(friendRequest.getFriendId()).orElse(null);
        return convertToFriendResponse(savedFriend, friendUser);
    }

    public List<FriendResponse> getUserFriends(Long userId) {
        List<Friend> friends = friendRepository.findByUserId(userId);
        return friends.stream().map(friend -> {
            User friendUser = userRepository.findById(friend.getFriendId()).orElse(null);
            return convertToFriendResponse(friend, friendUser);
        }).collect(Collectors.toList());
    }

    public FriendResponse updateFriend(Long userId, Long friendId, FriendRequest friendRequest) {
        Friend friend = friendRepository.findByUserIdAndFriendId(userId, friendId)
                .orElseThrow(() -> new RuntimeException("Friend relationship not found"));

        friend.setNickname(friendRequest.getNickname());
        friend.setPhone(friendRequest.getPhone());
        friend.setEmail(friendRequest.getEmail());
        friend.setAvatarUrl(friendRequest.getAvatarUrl());
        friend.setNotes(friendRequest.getNotes());
        friend.setIsFavorite(friendRequest.getIsFavorite());

        Friend updatedFriend = friendRepository.save(friend);

        User friendUser = userRepository.findById(friend.getFriendId()).orElse(null);
        return convertToFriendResponse(updatedFriend, friendUser);
    }

    public void removeFriend(Long userId, Long friendId) {
        friendRepository.deleteByUserIdAndFriendId(userId, friendId);
    }

    // Friend Group-related methods

    public FriendGroupResponse createFriendGroup(Long userId, FriendGroupRequest groupRequest) {
        FriendGroup group = new FriendGroup(userId, groupRequest.getName());
        group.setDescription(groupRequest.getDescription());
        group.setColor(groupRequest.getColor());
        group.setSortOrder(groupRequest.getSortOrder());

        FriendGroup savedGroup = friendGroupRepository.save(group);
        return convertToFriendGroupResponse(savedGroup);
    }

    public List<FriendGroupResponse> getUserFriendGroups(Long userId) {
        List<FriendGroup> groups = friendGroupRepository.findByUserIdOrderBySortOrderAsc(userId);
        return groups.stream().map(this::convertToFriendGroupResponse).collect(Collectors.toList());
    }

    public FriendGroupResponse updateFriendGroup(Long userId, Long groupId, FriendGroupRequest groupRequest) {
        FriendGroup group = friendGroupRepository.findById(groupId)
                .orElseThrow(() -> new RuntimeException("Friend group not found"));

        if (!group.getUserId().equals(userId)) {
            throw new RuntimeException("Unauthorized to update this friend group");
        }

        group.setName(groupRequest.getName());
        group.setDescription(groupRequest.getDescription());
        group.setColor(groupRequest.getColor());
        group.setSortOrder(groupRequest.getSortOrder());

        FriendGroup updatedGroup = friendGroupRepository.save(group);
        return convertToFriendGroupResponse(updatedGroup);
    }

    public void deleteFriendGroup(Long userId, Long groupId) {
        FriendGroup group = friendGroupRepository.findById(groupId)
                .orElseThrow(() -> new RuntimeException("Friend group not found"));

        if (!group.getUserId().equals(userId)) {
            throw new RuntimeException("Unauthorized to delete this friend group");
        }

        friendGroupRepository.deleteById(groupId);
    }

    // Helper methods

    private FriendResponse convertToFriendResponse(Friend friend, User friendUser) {
        FriendResponse response = new FriendResponse();
        response.setId(friend.getId());
        response.setUserId(friend.getUserId());
        response.setFriendId(friend.getFriendId());
        response.setNickname(friend.getNickname());
        response.setPhone(friend.getPhone());
        response.setEmail(friend.getEmail());
        response.setAvatarUrl(friend.getAvatarUrl());
        response.setNotes(friend.getNotes());
        response.setIsFavorite(friend.getIsFavorite());
        response.setCreatedAt(friend.getCreatedAt());
        response.setUpdatedAt(friend.getUpdatedAt());

        if (friendUser != null) {
            response.setUsername(friendUser.getUsername());
        }

        return response;
    }

    private FriendGroupResponse convertToFriendGroupResponse(FriendGroup group) {
        FriendGroupResponse response = new FriendGroupResponse();
        response.setId(group.getId());
        response.setUserId(group.getUserId());
        response.setName(group.getName());
        response.setDescription(group.getDescription());
        response.setColor(group.getColor());
        response.setSortOrder(group.getSortOrder());
        response.setCreatedAt(group.getCreatedAt());
        response.setUpdatedAt(group.getUpdatedAt());
        return response;
    }
}