package com.myproject.service;

import com.myproject.entity.GreetingTemplate;
import com.myproject.dto.GreetingTemplateRequest;
import com.myproject.dto.GreetingTemplateResponse;
import com.myproject.repository.GreetingTemplateRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class GreetingTemplateService {

    @Autowired
    private GreetingTemplateRepository greetingTemplateRepository;

    public GreetingTemplateResponse createTemplate(GreetingTemplateRequest request, Long userId) {
        GreetingTemplate template = new GreetingTemplate(
            request.getName(),
            request.getContent(),
            request.getStyleType(),
            request.getCategory(),
            request.getHolidayId(),
            request.getIsActive(),
            request.getLanguage(),
            userId
        );
        
        GreetingTemplate savedTemplate = greetingTemplateRepository.save(template);
        return mapToResponse(savedTemplate);
    }

    public GreetingTemplateResponse getTemplateById(Long id) {
        GreetingTemplate template = greetingTemplateRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Template not found with id: " + id));
        return mapToResponse(template);
    }

    public List<GreetingTemplateResponse> getAllTemplates() {
        return greetingTemplateRepository.findAll().stream()
            .map(this::mapToResponse)
            .collect(Collectors.toList());
    }

    public List<GreetingTemplateResponse> getActiveTemplates() {
        return greetingTemplateRepository.findByIsActiveTrue().stream()
            .map(this::mapToResponse)
            .collect(Collectors.toList());
    }

    public List<GreetingTemplateResponse> getTemplatesByCategory(String category) {
        return greetingTemplateRepository.findByCategoryAndIsActiveTrue(category).stream()
            .map(this::mapToResponse)
            .collect(Collectors.toList());
    }

    public List<GreetingTemplateResponse> getTemplatesByStyleType(String styleType) {
        return greetingTemplateRepository.findByStyleTypeAndIsActiveTrue(styleType).stream()
            .map(this::mapToResponse)
            .collect(Collectors.toList());
    }

    public List<GreetingTemplateResponse> getTemplatesByHolidayId(Long holidayId) {
        return greetingTemplateRepository.findByHolidayIdAndIsActiveTrue(holidayId).stream()
            .map(this::mapToResponse)
            .collect(Collectors.toList());
    }

    public List<GreetingTemplateResponse> searchTemplates(String keyword) {
        return greetingTemplateRepository.searchByKeyword(keyword).stream()
            .map(this::mapToResponse)
            .collect(Collectors.toList());
    }

    public GreetingTemplateResponse updateTemplate(Long id, GreetingTemplateRequest request) {
        GreetingTemplate existingTemplate = greetingTemplateRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Template not found with id: " + id));
            
        existingTemplate.setName(request.getName());
        existingTemplate.setContent(request.getContent());
        existingTemplate.setStyleType(request.getStyleType());
        existingTemplate.setCategory(request.getCategory());
        existingTemplate.setHolidayId(request.getHolidayId());
        existingTemplate.setIsActive(request.getIsActive());
        existingTemplate.setLanguage(request.getLanguage());
        
        GreetingTemplate updatedTemplate = greetingTemplateRepository.save(existingTemplate);
        return mapToResponse(updatedTemplate);
    }

    public void deleteTemplate(Long id) {
        GreetingTemplate template = greetingTemplateRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Template not found with id: " + id));
        greetingTemplateRepository.delete(template);
    }

    private GreetingTemplateResponse mapToResponse(GreetingTemplate template) {
        return new GreetingTemplateResponse(
            template.getId(),
            template.getName(),
            template.getContent(),
            template.getStyleType(),
            template.getCategory(),
            template.getHolidayId(),
            template.getIsActive(),
            template.getLanguage(),
            template.getCreatedBy(),
            template.getCreatedAt(),
            template.getUpdatedAt()
        );
    }
}