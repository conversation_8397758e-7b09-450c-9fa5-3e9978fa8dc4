-- Holidays Table
CREATE TABLE IF NOT EXISTS holidays (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HAR(255) NOT NULL,
    date DATE NOT NULL,
    holiday_type VARCHAR(100),
    description VARCHAR(1000),
    is_recurring BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Indexes for better query performance
CREATE INDEX idx_holidays_date ON holidays(date);
CREATE INDEX idx_holidays_type ON holidays(holiday_type);
CREATE INDEX idx_holidays_is_recurring ON holidays(is_recurring);