-- Sample data for holidays table
INSERT INTO holidays (name, date, holiday_type, description, is_recurring, created_at, updated_at) VALUES
('春节', '2024-02-10', 'traditional', '中国最重要的传统节日，农历新年', true, NOW(), NOW()),
('中秋节', '2024-09-15', 'traditional', '中国传统节日，赏月吃月饼', true, NOW(), NOW()),
('国庆节', '2024-10-01', 'national', '中华人民共和国国庆节', true, NOW(), NOW()),
('情人节', '2024-02-14', 'western', '西方情人节', true, NOW(), NOW()),
('母亲节', '2024-05-12', 'western', '感恩母亲的节日', true, NOW(), NOW()),
('父亲节', '2024-06-16', 'western', '感恩父亲的节日', true, NOW(), NOW()),
('圣诞节', '2024-12-25', 'western', '基督教节日，庆祝耶稣诞生', true, NOW(), NOW()),
('元旦', '2024-01-01', 'international', '公历新年', true, NOW(), NOW());

-- English holidays
INSERT INTO holidays (name, date, holiday_type, description, is_recurring, created_at, updated_at) VALUES
('New Year', '2024-01-01', 'international', 'Celebration of the new calendar year', true, NOW(), NOW()),
('Valentine''s Day', '2024-02-14', 'western', 'A day for celebrating love and affection', true, NOW(), NOW()),
('Independence Day', '2024-07-04', 'national', 'American Independence Day', true, NOW(), NOW()),
('Halloween', '2024-10-31', 'western', 'A celebration observed in many countries on 31 October', true, NOW(), NOW()),
('Thanksgiving', '2024-11-28', 'western', 'A national holiday celebrated on the fourth Thursday of November', true, NOW(), NOW()),
('Christmas', '2024-12-25', 'western', 'Celebration of the birth of Jesus Christ', true, NOW(), NOW());