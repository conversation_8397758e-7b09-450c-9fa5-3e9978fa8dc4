# 传统节日问候助手项目精简技术文档包

## 核心技术文档

### 1. 项目概述与快速开始
**文件**: `/Users/<USER>/MyProject/README.md`
**核心内容**:
- 技术栈: Spring Boot 3.x (Java 17) + Vue 3 (TypeScript)
- 架构: 前后端分离 + Docker容器化部署
- 数据库: MySQL 8.0 + Redis缓存
- 快速启动命令和环境配置说明

### 2. 系统架构设计
**文件**: `/Users/<USER>/MyProject/docs/architecture/01_system_architecture.md`
**核心内容**:
- 前后端分离架构图
- 模块划分: 用户认证、节日管理、好友管理、问候模板、邮件服务
- 数据流向: 前端 ↔ API网关 ↔ 后端服务 ↔ 数据库
- 安全设计: JWT认证 + Spring Security

### 3. 数据库设计
**文件**: `/Users/<USER>/MyProject/docs/database/01_database_design.md`
**核心内容**:
- 8个核心表结构:
  - users: 用户信息表
  - holidays: 节日信息表
  - friends: 好友信息表
  - friend_groups: 好友分组表
  - greeting_templates: 问候模板表
  - user_greetings: 用户问候设置表
  - email_logs: 邮件发送记录表
  - roles/user_roles: 角色权限表
- 表关系图和索引策略

### 4. API接口规范
**文件**: `/Users/<USER>/MyProject/docs/api/01_api_specification.md`
**核心内容**:
- 认证接口: 注册/登录/刷新令牌
- 用户接口: 个人信息管理/提醒时间设置
- 节日接口: 节日列表/详情查询
- 好友接口: 好友增删改查/分组管理
- 模板接口: 模板管理/分类查询
- 问候接口: 问候设置/发送记录
- 所有接口的请求/响应格式和状态码

### 5. 核心功能需求
**文件**: `/Users/<USER>/MyProject/docs/development/02_requirements_analysis.md`
**核心内容**:
- 用户注册登录系统
- 节日数据展示和管理
- 好友信息管理(增删改查/分组)
- 问候模板选择和自定义
- 邮件提醒功能(定时发送)
- 响应式UI设计

## 职位技术要求

### 后端开发工程师
**核心任务**:
1. 实现用户认证系统(JWT + Spring Security)
2. 开发节日管理API(CRUD操作)
3. 实现好友管理系统(好友信息+分组)
4. 开发问候模板系统(模板管理)
5. 实现邮件提醒服务(定时任务+邮件发送)
6. 数据库设计优化和API文档维护

**技术栈要求**:
- Java 17 + Spring Boot 3.x
- MySQL 8.0 + JPA/Hibernate
- Redis缓存
- JWT安全认证
- Spring Mail邮件服务
- JUnit 5 + Mockito测试

### 前端开发工程师
**核心任务**:
1. 实现用户界面(登录注册+个人中心)
2. 开发节日展示页面(列表+详情)
3. 实现好友管理界面(增删改查)
4. 开发问候模板页面(选择+编辑)
5. 实现邮件记录页面(发送历史)
6. 响应式设计和移动端适配

**技术栈要求**:
- Vue 3 + TypeScript
- Vite构建工具
- Tailwind CSS样式框架
- Axios HTTP客户端
- Pinia状态管理
- Vue Router路由
- Vitest/Cypress测试

### 测试工程师
**核心任务**:
1. 制定测试计划和用例设计
2. 执行功能测试和回归测试
3. 实施自动化测试(JUnit/Vitest)
4. 进行API接口测试(Postman)
5. 编写测试报告和缺陷分析
6. 性能测试(JMeter)和安全测试

**技术要求**:
- 单元测试: JUnit 5 + Mockito
- 集成测试: Spring Boot Test
- 前端测试: Vitest + Cypress
- API测试: Postman
- 性能测试: JMeter
- 安全测试: OWASP ZAP

### DevOps工程师
**核心任务**:
1. Docker容器化部署配置
2. Nginx反向代理和负载均衡
3. CI/CD流水线建立(GitHub Actions)
4. 监控告警系统(Prometheus + Grafana)
5. 生产环境管理和备份恢复
6. 系统性能优化和安全加固

**技术要求**:
- Docker + Docker Compose
- Nginx配置和优化
- GitHub Actions CI/CD
- Prometheus + Grafana监控
- Linux系统管理
- 安全加固和漏洞扫描

## 核心配置文件

### 环境配置
**文件**: 
- 开发环境: `/Users/<USER>/MyProject/.env`
- 生产环境: `/Users/<USER>/MyProject/.env.prod`

**关键配置项**:
- 数据库连接信息
- Redis配置
- JWT密钥和过期时间
- 邮件服务配置
- 服务器端口配置

### 容器编排
**文件**: `/Users/<USER>/MyProject/docker-compose.yml`
**内容**: 开发环境的多容器编排配置

### 启动脚本
**文件**: `/Users/<USER>/MyProject/start.sh`
**功能**: 一键启动开发环境

## 开发规范

### 代码规范
**文件**: `/Users/<USER>/MyProject/docs/development/01_project_guidelines.md`
**要点**:
- Git分支策略: Git Flow
- 代码审查: Pull Request机制
- 提交规范: Conventional Commits
- 命名规范: 统一的命名约定

### 协作规范
**文件**: `/Users/<USER>/MyProject/docs/development/03_collaboration_guidelines.md`
**要点**:
- 前后端接口联调流程
- Mock数据使用规范
- 版本兼容性管理
- 错误处理统一规范

## 测试策略

**文件**: `/Users/<USER>/MyProject/docs/testing/01_testing_strategy.md`
**核心要求**:
- 单元测试覆盖率 ≥ 80%
- 集成测试覆盖率 ≥ 90%
- 端到端测试覆盖率 ≥ 95%
- 性能测试响应时间 < 500ms
- 安全测试无高危漏洞

## 部署规范

**文件**: `/Users/<USER>/MyProject/docs/deployment/01_deployment_operations.md`
**核心内容**:
- 生产环境部署架构
- 容器化部署流程
- 负载均衡配置
- 监控告警机制
- 备份恢复策略

---

这份精简文档包聚焦于技术实现细节，去除了所有人员管理相关内容，只保留职位的技术要求和任务分配，便于您根据这些要求分配任务给不同的Agent。