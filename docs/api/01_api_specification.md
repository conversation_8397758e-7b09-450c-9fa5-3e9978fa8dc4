# 传统节日问候助手API接口设计规范

## 1. 设计原则

### 1.1 RESTful API设计规范
- 使用标准HTTP方法 (GET, POST, PUT, DELETE)
- 资源URI使用名词复数形式
- 正确使用HTTP状态码
- 支持JSON数据格式
- 版本化API设计

### 1.2 响应格式规范
- 统一的成功响应格式
- 标准化的错误响应格式
- 一致的时间格式 (ISO 8601)
- 明确的数据类型定义

### 1.3 安全规范
- JWT Token认证
- 请求签名验证
- 数据传输加密 (HTTPS)
- 输入参数校验
- 速率限制

## 2. API版本管理

### 2.1 版本标识
所有API端点都包含版本号：
```
/v1/api/{资源名}
示例: /v1/api/users
```

### 2.2 版本演进策略
- 向后兼容性保证
- 废弃API标记
- 迁移指南提供
- 版本生命周期管理

## 3. 认证授权API

### 3.1 用户注册
```
POST /v1/api/auth/signup

请求头:
Content-Type: application/json

请求体:
{
  "username": "string",           // 用户名 (3-50字符)
  "email": "string",              // 邮箱地址 (有效的邮箱格式)
  "password": "string"            // 密码 (6-40字符)
}

成功响应 (201 Created):
{
  "message": "User registered successfully"
}

错误响应:
400 Bad Request:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 400,
  "error": "Bad Request",
  "message": "Validation failed for argument",
  "path": "/v1/api/auth/signup"
}

409 Conflict:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 409,
  "error": "Conflict",
  "message": "Username or email already exists",
  "path": "/v1/api/auth/signup"
}
```

### 3.2 用户登录
```
POST /v1/api/auth/signin

请求头:
Content-Type: application/json

请求体:
{
  "email": "string",              // 邮箱地址
  "password": "string"            // 密码
}

成功响应 (200 OK):
{
  "token": "string",               // JWT访问令牌
  "tokenType": "Bearer",           // 令牌类型
  "expiresIn": 86400,              // 过期时间(秒)
  "user": {
    "id": 1,                       // 用户ID
    "username": "string",          // 用户名
    "email": "string"              // 邮箱地址
  }
}

错误响应:
401 Unauthorized:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 401,
  "error": "Unauthorized",
  "message": "Invalid credentials",
  "path": "/v1/api/auth/signin"
}
```

### 3.3 刷新令牌
```
POST /v1/api/auth/refresh

请求头:
Authorization: Bearer {refresh_token}
Content-Type: application/json

成功响应 (200 OK):
{
  "token": "string",               // 新的访问令牌
  "tokenType": "Bearer",
  "expiresIn": 86400
}

错误响应:
401 Unauthorized:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 401,
  "error": "Unauthorized",
  "message": "Invalid refresh token",
  "path": "/v1/api/auth/refresh"
}
```

## 4. 用户管理API

### 4.1 获取当前用户信息
```
GET /v1/api/users/me

请求头:
Authorization: Bearer {access_token}

成功响应 (200 OK):
{
  "id": 1,
  "username": "string",
  "email": "string",
  "avatarUrl": "string",
  "phone": "string",
  "createdAt": "2025-08-20T10:30:00Z",
  "updatedAt": "2025-08-20T10:30:00Z"
}

错误响应:
401 Unauthorized:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 401,
  "error": "Unauthorized",
  "message": "Access token required",
  "path": "/v1/api/users/me"
}
```

### 4.2 更新用户信息
```
PUT /v1/api/users/me

请求头:
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "username": "string",            // 可选
  "email": "string",               // 可选
  "avatarUrl": "string",           // 可选
  "phone": "string"                 // 可选
}

成功响应 (200 OK):
{
  "id": 1,
  "username": "string",
  "email": "string",
  "avatarUrl": "string",
  "phone": "string",
  "createdAt": "2025-08-20T10:30:00Z",
  "updatedAt": "2025-08-20T10:30:00Z"
}

错误响应:
400 Bad Request:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 400,
  "error": "Bad Request",
  "message": "Validation failed",
  "path": "/v1/api/users/me"
}

409 Conflict:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 409,
  "error": "Conflict",
  "message": "Username or email already exists",
  "path": "/v1/api/users/me"
}
```

### 4.3 更新提醒时间
```
PUT /v1/api/users/me/reminder-time

请求头:
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "reminderTime": "10:30:00"       // 提醒时间 (HH:MM:SS格式)
}

成功响应 (200 OK):
{
  "id": 1,
  "username": "string",
  "email": "string",
  "reminderTime": "10:30:00",
  "createdAt": "2025-08-20T10:30:00Z",
  "updatedAt": "2025-08-20T10:30:00Z"
}

错误响应:
400 Bad Request:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 400,
  "error": "Bad Request",
  "message": "Invalid time format",
  "path": "/v1/api/users/me/reminder-time"
}
```

### 4.4 修改密码
```
PUT /v1/api/users/me/password

请求头:
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "currentPassword": "string",     // 当前密码
  "newPassword": "string"          // 新密码 (6-40字符)
}

成功响应 (200 OK):
{
  "message": "Password updated successfully"
}

错误响应:
400 Bad Request:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 400,
  "error": "Bad Request",
  "message": "Current password is incorrect",
  "path": "/v1/api/users/me/password"
}

401 Unauthorized:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 401,
  "error": "Unauthorized",
  "message": "Access token required",
  "path": "/v1/api/users/me/password"
}
```

## 5. 节日管理API

### 5.1 获取节日列表
```
GET /v1/api/holidays

查询参数:
- page: 页码 (默认: 1)
- size: 每页大小 (默认: 10, 最大: 100)
- type: 节日类型筛选
- year: 年份筛选
- month: 月份筛选

请求头:
Authorization: Bearer {access_token}

成功响应 (200 OK):
{
  "content": [
    {
      "id": 1,
      "name": "春节",
      "date": "2025-01-29",
      "holidayType": "传统节日",
      "description": "中国最重要的传统节日",
      "isRecurring": true,
      "createdAt": "2025-08-20T10:30:00Z"
    }
  ],
  "page": 1,
  "size": 10,
  "totalElements": 100,
  "totalPages": 10
}

错误响应:
401 Unauthorized:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 401,
  "error": "Unauthorized",
  "message": "Access token required",
  "path": "/v1/api/holidays"
}
```

### 5.2 获取节日详情
```
GET /v1/api/holidays/{id}

请求头:
Authorization: Bearer {access_token}

成功响应 (200 OK):
{
  "id": 1,
  "name": "春节",
  "date": "2025-01-29",
  "holidayType": "传统节日",
  "description": "中国最重要的传统节日",
  "isRecurring": true,
  "createdAt": "2025-08-20T10:30:00Z",
  "updatedAt": "2025-08-20T10:30:00Z"
}

错误响应:
404 Not Found:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 404,
  "error": "Not Found",
  "message": "Holiday not found",
  "path": "/v1/api/holidays/1"
}
```

## 6. 好友管理API

### 6.1 获取好友列表
```
GET /v1/api/friends

查询参数:
- page: 页码 (默认: 1)
- size: 每页大小 (默认: 10, 最大: 100)
- groupId: 分组ID筛选
- favorite: 收藏筛选 (true/false)
- keyword: 关键词搜索

请求头:
Authorization: Bearer {access_token}

成功响应 (200 OK):
{
  "content": [
    {
      "id": 1,
      "userId": 1,
      "friendId": 2,
      "nickname": "张三",
      "email": "<EMAIL>",
      "phone": "13800138000",
      "avatarUrl": "string",
      "notes": "string",
      "isFavorite": true,
      "groupId": 1,
      "createdAt": "2025-08-20T10:30:00Z",
      "updatedAt": "2025-08-20T10:30:00Z"
    }
  ],
  "page": 1,
  "size": 10,
  "totalElements": 100,
  "totalPages": 10
}
```

### 6.2 添加好友
```
POST /v1/api/friends

请求头:
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "nickname": "string",            // 好友昵称
  "email": "string",               // 好友邮箱
  "phone": "string",               // 好友电话 (可选)
  "avatarUrl": "string",           // 头像URL (可选)
  "notes": "string",               // 备注 (可选)
  "groupId": 1                      // 分组ID (可选)
}

成功响应 (201 Created):
{
  "id": 1,
  "userId": 1,
  "friendId": 2,
  "nickname": "string",
  "email": "string",
  "phone": "string",
  "avatarUrl": "string",
  "notes": "string",
  "isFavorite": false,
  "groupId": 1,
  "createdAt": "2025-08-20T10:30:00Z",
  "updatedAt": "2025-08-20T10:30:00Z"
}

错误响应:
400 Bad Request:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 400,
  "error": "Bad Request",
  "message": "Validation failed",
  "path": "/v1/api/friends"
}
```

### 6.3 更新好友信息
```
PUT /v1/api/friends/{id}

请求头:
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "nickname": "string",            // 好友昵称
  "email": "string",               // 好友邮箱
  "phone": "string",               // 好友电话 (可选)
  "avatarUrl": "string",           // 头像URL (可选)
  "notes": "string",               // 备注 (可选)
  "groupId": 1                     // 分组ID (可选)
}

成功响应 (200 OK):
{
  "id": 1,
  "userId": 1,
  "friendId": 2,
  "nickname": "string",
  "email": "string",
  "phone": "string",
  "avatarUrl": "string",
  "notes": "string",
  "isFavorite": false,
  "groupId": 1,
  "createdAt": "2025-08-20T10:30:00Z",
  "updatedAt": "2025-08-20T10:30:00Z"
}

错误响应:
404 Not Found:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 404,
  "error": "Not Found",
  "message": "Friend not found",
  "path": "/v1/api/friends/1"
}
```

### 6.4 删除好友
```
DELETE /v1/api/friends/{id}

请求头:
Authorization: Bearer {access_token}

成功响应 (204 No Content)

错误响应:
404 Not Found:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 404,
  "error": "Not Found",
  "message": "Friend not found",
  "path": "/v1/api/friends/1"
}
```

### 6.5 收藏/取消收藏好友
```
PUT /v1/api/friends/{id}/favorite

请求头:
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "isFavorite": true               // 是否收藏
}

成功响应 (200 OK):
{
  "id": 1,
  "userId": 1,
  "friendId": 2,
  "nickname": "string",
  "email": "string",
  "phone": "string",
  "avatarUrl": "string",
  "notes": "string",
  "isFavorite": true,
  "groupId": 1,
  "createdAt": "2025-08-20T10:30:00Z",
  "updatedAt": "2025-08-20T10:30:00Z"
}
```

## 7. 好友分组API

### 7.1 获取分组列表
```
GET /v1/api/friend-groups

请求头:
Authorization: Bearer {access_token}

成功响应 (200 OK):
[
  {
    "id": 1,
    "userId": 1,
    "name": "家人",
    "description": "家庭成员",
    "color": "#FF0000",
    "sortOrder": 1,
    "createdAt": "2025-08-20T10:30:00Z",
    "updatedAt": "2025-08-20T10:30:00Z"
  }
]
```

### 7.2 创建分组
```
POST /v1/api/friend-groups

请求头:
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "name": "string",                // 分组名称
  "description": "string",         // 分组描述 (可选)
  "color": "#RRGGBB",              // 颜色 (可选)
  "sortOrder": 1                   // 排序序号 (可选)
}

成功响应 (201 Created):
{
  "id": 1,
  "userId": 1,
  "name": "string",
  "description": "string",
  "color": "#RRGGBB",
  "sortOrder": 1,
  "createdAt": "2025-08-20T10:30:00Z",
  "updatedAt": "2025-08-20T10:30:00Z"
}
```

### 7.3 更新分组
```
PUT /v1/api/friend-groups/{id}

请求头:
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "name": "string",                // 分组名称
  "description": "string",        // 分组描述 (可选)
  "color": "#RRGGBB",              // 颜色 (可选)
  "sortOrder": 1                   // 排序序号 (可选)
}

成功响应 (200 OK):
{
  "id": 1,
  "userId": 1,
  "name": "string",
  "description": "string",
  "color": "#RRGGBB",
  "sortOrder": 1,
  "createdAt": "2025-08-20T10:30:00Z",
  "updatedAt": "2025-08-20T10:30:00Z"
}
```

## 8. 问候模板API

### 8.1 获取模板列表
```
GET /v1/api/templates

查询参数:
- page: 页码 (默认: 1)
- size: 每页大小 (默认: 10, 最大: 100)
- category: 分类筛选
- holidayId: 节日ID筛选
- language: 语言筛选
- public: 公开模板筛选

请求头:
Authorization: Bearer {access_token}

成功响应 (200 OK):
{
  "content": [
    {
      "id": 1,
      "name": "春节问候模板",
      "content": "亲爱的{name}，祝您春节快乐！",
      "styleType": "formal",
      "category": "节日祝福",
      "holidayId": 1,
      "isActive": true,
      "language": "zh",
      "isPublic": true,
      "usageCount": 100,
      "createdBy": 1,
      "createdAt": "2025-08-20T10:30:00Z",
      "updatedAt": "2025-08-20T10:30:00Z"
    }
  ],
  "page": 1,
  "size": 10,
  "totalElements": 100,
  "totalPages": 10
}
```

### 8.2 创建模板
```
POST /v1/api/templates

请求头:
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "name": "string",                // 模板名称
  "content": "string",             // 模板内容
  "styleType": "string",           // 样式类型 (可选)
  "category": "string",            // 分类 (可选)
  "holidayId": 1,                  // 适用节日ID (可选)
  "language": "zh",                // 语言 (可选, 默认: zh)
  "isPublic": false                // 是否公开 (可选, 默认: false)
}

成功响应 (201 Created):
{
  "id": 1,
  "name": "string",
  "content": "string",
  "styleType": "string",
  "category": "string",
  "holidayId": 1,
  "isActive": true,
  "language": "zh",
  "isPublic": false,
  "usageCount": 0,
  "createdBy": 1,
  "createdAt": "2025-08-20T10:30:00Z",
  "updatedAt": "2025-08-20T10:30:00Z"
}
```

## 9. 问候设置API

### 9.1 获取用户问候设置
```
GET /v1/api/greetings

查询参数:
- page: 页码 (默认: 1)
- size: 每页大小 (默认: 10, 最大: 100)
- holidayId: 节日ID筛选
- friendId: 好友ID筛选
- enabled: 启用状态筛选

请求头:
Authorization: Bearer {access_token}

成功响应 (200 OK):
{
  "content": [
    {
      "id": 1,
      "userId": 1,
      "friendId": 2,
      "holidayId": 1,
      "templateId": 1,
      "customMessage": "string",
      "sendTime": "10:30:00",
      "isEnabled": true,
      "lastSentAt": "2025-01-29T10:30:00Z",
      "nextSendAt": "2026-01-29T10:30:00Z",
      "createdAt": "2025-08-20T10:30:00Z",
      "updatedAt": "2025-08-20T10:30:00Z"
    }
  ],
  "page": 1,
  "size": 10,
  "totalElements": 100,
  "totalPages": 10
}
```

### 9.2 创建问候设置
```
POST /v1/api/greetings

请求头:
Authorization: Bearer {access_token}
Content-Type: application/json

请求体:
{
  "friendId": 2,                   // 好友ID
  "holidayId": 1,                  // 节日ID
  "templateId": 1,                 // 模板ID (可选)
  "customMessage": "string",       // 自定义消息 (可选)
  "sendTime": "10:30:00",          // 发送时间
  "isEnabled": true                // 是否启用
}

成功响应 (201 Created):
{
  "id": 1,
  "userId": 1,
  "friendId": 2,
  "holidayId": 1,
  "templateId": 1,
  "customMessage": "string",
  "sendTime": "10:30:00",
  "isEnabled": true,
  "lastSentAt": null,
  "nextSendAt": "2026-01-29T10:30:00Z",
  "createdAt": "2025-08-20T10:30:00Z",
  "updatedAt": "2025-08-20T10:30:00Z"
}
```

## 10. 邮件记录API

### 10.1 获取邮件发送记录
```
GET /v1/api/email-logs

查询参数:
- page: 页码 (默认: 1)
- size: 每页大小 (默认: 10, 最大: 100)
- status: 发送状态筛选
- startDate: 开始日期
- endDate: 结束日期

请求头:
Authorization: Bearer {access_token}

成功响应 (200 OK):
{
  "content": [
    {
      "id": 1,
      "userId": 1,
      "friendId": 2,
      "greetingId": 1,
      "subject": "春节问候",
      "content": "亲爱的张三，祝您春节快乐！",
      "status": "sent",
      "errorMessage": null,
      "sentAt": "2025-01-29T10:30:00Z",
      "retryCount": 0,
      "createdAt": "2025-01-29T10:25:00Z"
    }
  ],
  "page": 1,
  "size": 10,
  "totalElements": 100,
  "totalPages": 10
}
```

## 11. 错误响应格式

### 11.1 标准错误响应
```json
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 400,
  "error": "Bad Request",
  "message": "Validation failed for argument",
  "path": "/v1/api/resource"
}
```

### 11.2 常见HTTP状态码
- **200 OK**: 请求成功
- **201 Created**: 创建成功
- **204 No Content**: 删除成功
- **400 Bad Request**: 请求参数错误
- **401 Unauthorized**: 未授权访问
- **403 Forbidden**: 禁止访问
- **404 Not Found**: 资源不存在
- **409 Conflict**: 资源冲突
- **500 Internal Server Error**: 服务器内部错误

## 12. 分页响应格式

### 12.1 分页响应结构
```json
{
  "content": [...],                // 数据内容数组
  "page": 1,                       // 当前页码
  "size": 10,                      // 每页大小
  "totalElements": 100,             // 总元素数量
  "totalPages": 10                 // 总页数
}
```

## 13. 请求频率限制

### 13.1 限制策略
- 普通用户: 100次/分钟
- 认证用户: 1000次/分钟
- 管理员用户: 5000次/分钟

### 13.2 超限响应
```
429 Too Many Requests:
{
  "timestamp": "2025-08-20T10:30:00Z",
  "status": 429,
  "error": "Too Many Requests",
  "message": "Rate limit exceeded. Try again in 60 seconds.",
  "path": "/v1/api/resource"
}
```

## 14. API文档生成

### 14.1 Swagger集成
- 使用Springfox Swagger自动生成API文档
- 提供在线API测试界面
- 支持多种文档格式导出

### 14.2 文档访问地址
```
开发环境: http://localhost:8080/swagger-ui.html
测试环境: https://test-api.example.com/swagger-ui.html
生产环境: https://api.example.com/swagger-ui.html
```