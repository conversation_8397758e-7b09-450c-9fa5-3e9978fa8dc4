# 传统节日问候助手项目开发规范文档

## 1. 项目概述

### 1.1 项目背景
传统节日问候助手是一个帮助用户在重要节日向亲朋好友发送问候的Web应用。用户可以设置提醒时间，系统会在节日当天自动发送问候邮件。

### 1.2 项目目标
- 提供用户友好的节日问候管理界面
- 支持自定义提醒时间设置
- 自动发送节日问候邮件
- 管理好友联系信息
- 提供多种问候模板选择

### 1.3 MVP功能范围
1. 用户注册登录系统
2. 节日数据展示
3. 好友信息管理
4. 问候模板选择与编辑
5. 邮件提醒功能
6. 响应式UI设计

## 2. 开发流程规范

### 2.1 敏捷开发流程
```
需求分析 → 系统设计 → 技术选型 → 数据库设计 → API设计 → 前后端并行开发 → 集成测试 → 部署上线
```

### 2.2 开发阶段划分

#### 阶段一：需求分析与设计（1-2周）
- 完成详细需求文档
- 系统架构设计
- 数据库设计
- API接口设计
- UI/UX设计稿

#### 阶段二：后端开发（2-3周）
- 搭建后端框架
- 实现用户认证系统
- 开发核心业务逻辑
- 实现API接口
- 单元测试

#### 阶段三：前端开发（2-3周）
- 搭建前端框架
- 实现UI组件
- 集成API调用
- 响应式设计
- 前端测试

#### 阶段四：集成与测试（1周）
- 前后端集成
- 系统测试
- 性能优化
- Bug修复

#### 阶段五：部署与上线（0.5周）
- 环境部署
- 数据迁移
- 上线验证

## 3. 技术架构规范

### 3.1 后端技术栈
- **框架**: Spring Boot 3.x (Java 17)
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **安全**: Spring Security + JWT
- **邮件**: Spring Mail (Gmail SMTP)
- **构建工具**: Maven
- **API规范**: RESTful API

### 3.2 前端技术栈
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router
- **UI库**: Tailwind CSS
- **HTTP客户端**: Axios

### 3.3 部署架构
- **反向代理**: Nginx
- **容器化**: Docker + Docker Compose
- **监控**: Spring Boot Actuator

## 4. 数据库设计规范

### 4.1 命名规范
- 表名: 小写字母 + 下划线 (users, greeting_templates)
- 字段名: 小写字母 + 下划线 (user_id, created_at)
- 索引名: idx_表名_字段名 (idx_users_email)

### 4.2 核心表结构

#### users 表
```sql
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    reminder_time TIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### roles 表
```sql
CREATE TABLE roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### user_roles 表
```sql
CREATE TABLE user_roles (
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);
```

#### holidays 表
```sql
CREATE TABLE holidays (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    date DATE NOT NULL,
    holiday_type VARCHAR(100),
    description TEXT,
    is_recurring BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### friends 表
```sql
CREATE TABLE friends (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    friend_id BIGINT NOT NULL,
    nickname VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    avatar_url VARCHAR(255),
    notes TEXT,
    is_favorite BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (friend_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### greeting_templates 表
```sql
CREATE TABLE greeting_templates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    style_type VARCHAR(100),
    category VARCHAR(100),
    holiday_id BIGINT,
    is_active BOOLEAN DEFAULT TRUE,
    language VARCHAR(10) DEFAULT 'zh',
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (holiday_id) REFERENCES holidays(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);
```

## 5. API接口设计规范

### 5.1 接口设计原则
- 遵循RESTful API设计规范
- 使用HTTP状态码表示操作结果
- 统一的错误响应格式
- 版本化API设计

### 5.2 认证相关接口

#### 用户注册
```
POST /api/auth/signup
Content-Type: application/json

请求体:
{
  "username": "string",
  "email": "string",
  "password": "string"
}

响应:
201 Created
{
  "message": "User registered successfully"
}

400 Bad Request
{
  "error": "Error message"
}
```

#### 用户登录
```
POST /api/auth/signin
Content-Type: application/json

请求体:
{
  "email": "string",
  "password": "string"
}

响应:
200 OK
{
  "token": "jwt_token",
  "type": "Bearer",
  "id": 1,
  "username": "string",
  "email": "string"
}

401 Unauthorized
{
  "error": "Invalid credentials"
}
```

### 5.3 用户相关接口

#### 获取用户信息
```
GET /api/users/{id}
Authorization: Bearer jwt_token

响应:
200 OK
{
  "id": 1,
  "username": "string",
  "email": "string",
  "reminderTime": "10:00:00"
}
```

#### 更新提醒时间
```
PUT /api/users/{id}/reminder-time
Authorization: Bearer jwt_token
Content-Type: application/json

请求体:
{
  "reminderTime": "10:00:00"
}

响应:
200 OK
{
  "id": 1,
  "username": "string",
  "email": "string",
  "reminderTime": "10:00:00"
}
```

### 5.4 节日相关接口

#### 获取所有节日
```
GET /api/holidays
Authorization: Bearer jwt_token

响应:
200 OK
[
  {
    "id": 1,
    "name": "春节",
    "date": "2025-01-29",
    "holidayType": "传统节日",
    "description": "中国最重要的传统节日",
    "isRecurring": true
  }
]
```

### 5.5 好友相关接口

#### 获取用户好友列表
```
GET /api/friends
Authorization: Bearer jwt_token

响应:
200 OK
[
  {
    "id": 1,
    "userId": 1,
    "friendId": 2,
    "nickname": "张三",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "isFavorite": true
  }
]
```

#### 添加好友
```
POST /api/friends
Authorization: Bearer jwt_token
Content-Type: application/json

请求体:
{
  "friendId": 2,
  "nickname": "张三",
  "email": "<EMAIL>",
  "phone": "13800138000"
}

响应:
201 Created
{
  "id": 1,
  "userId": 1,
  "friendId": 2,
  "nickname": "张三",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "isFavorite": false
}
```

## 6. 前后端协作规范

### 6.1 开发顺序
1. **第1周**: 完成需求分析、系统设计、数据库设计
2. **第2周**: 后端框架搭建、API接口定义
3. **第3-4周**: 后端核心功能开发
4. **第5周**: 前端框架搭建、UI组件设计
5. **第6-7周**: 前端功能开发、API集成
6. **第8周**: 集成测试、Bug修复
7. **第9周**: 部署上线

### 6.2 接口协作
- 前后端共同制定API文档
- 使用Swagger/OpenAPI规范接口文档
- 建立Mock数据用于前端开发
- 定期进行接口联调

### 6.3 代码管理
- 使用Git进行版本控制
- 采用Git Flow分支策略
- 代码审查机制
- 自动化测试集成

## 7. 测试策略

### 7.1 测试类型
- 单元测试 (80%覆盖率)
- 集成测试
- 端到端测试
- 性能测试
- 安全测试

### 7.2 测试工具
- 后端: JUnit 5, Mockito
- 前端: Vitest, Cypress
- API测试: Postman

## 8. 部署规范

### 8.1 环境配置
- 开发环境: 本地Docker容器
- 测试环境: 云服务器测试实例
- 生产环境: 负载均衡 + 多实例部署

### 8.2 部署流程
1. 代码构建
2. Docker镜像打包
3. 镜像推送至仓库
4. 自动化部署到服务器
5. 健康检查
6. 回滚机制

### 8.3 监控告警
- 应用性能监控
- 数据库性能监控
- 错误日志收集
- 服务可用性监控

## 9. 项目管理规范

### 9.1 任务管理
- 使用看板管理开发任务
- 每日站会同步进度
- 每周迭代回顾
- 风险识别与管理

### 9.2 文档管理
- 需求文档版本控制
- 技术文档持续更新
- API文档自动生成
- 部署文档维护