# 传统节日问候助手前后端开发协作规范

## 1. 开发流程规范

### 1.1 敏捷开发流程
```
需求分析 → 系统设计 → 技术选型 → 数据库设计 → API设计 → 前后端并行开发 → 集成测试 → 部署上线
```

### 1.2 迭代周期
- **迭代周期**: 2周一个迭代
- **每日站会**: 上午9:30
- **迭代评审**: 每个迭代结束时
- **回顾会议**: 迭代评审后进行

### 1.3 任务管理
- 使用Jira/Trello管理开发任务
- 任务状态: Todo → In Progress → Code Review → Done
- 优先级: High → Medium → Low
- 估算单位: Story Points

## 2. 开发阶段规划

### 2.1 第一阶段：基础框架搭建 (Week 1-2)
**目标**: 完成前后端基础框架搭建和核心配置

**后端任务**:
- 搭建Spring Boot项目结构
- 配置数据库连接和JPA
- 配置Spring Security和JWT
- 实现用户注册登录API
- 编写基础单元测试

**前端任务**:
- 搭建Vue 3项目结构
- 配置Vite和TypeScript
- 配置Tailwind CSS
- 实现基础路由配置
- 创建公共组件库

**交付物**:
- 后端: 用户认证API可正常使用
- 前端: 基础页面框架和路由导航

### 2.2 第二阶段：核心功能开发 (Week 3-6)
**目标**: 完成用户管理、节日管理、好友管理核心功能

**后端任务**:
- 实现用户信息管理API
- 实现节日数据管理API
- 实现好友信息管理API
- 实现分组管理API
- 编写完整单元测试

**前端任务**:
- 实现用户个人信息页面
- 实现节日展示页面
- 实现好友管理页面
- 实现分组管理功能
- 集成API调用和状态管理

**交付物**:
- 后端: 核心业务API全部完成
- 前端: 核心功能页面可正常使用

### 2.3 第三阶段：高级功能开发 (Week 7-8)
**目标**: 完成问候模板、邮件提醒等高级功能

**后端任务**:
- 实现问候模板管理API
- 实现邮件发送服务
- 实现定时任务调度
- 实现邮件记录管理
- 编写集成测试

**前端任务**:
- 实现模板管理页面
- 实现问候设置页面
- 实现邮件记录页面
- 实现通知提醒功能
- 优化用户体验

**交付物**:
- 后端: 高级功能API全部完成
- 前端: 高级功能页面可正常使用

### 2.4 第四阶段：测试与优化 (Week 9)
**目标**: 完成系统测试、性能优化和Bug修复

**后端任务**:
- 进行压力测试
- 优化数据库查询性能
- 修复测试发现的问题
- 完善日志和监控

**前端任务**:
- 进行兼容性测试
- 优化页面加载性能
- 修复UI显示问题
- 完善错误处理

**交付物**:
- 稳定可靠的完整系统

## 3. 接口协作规范

### 3.1 API文档管理
- 使用Swagger/OpenAPI规范编写API文档
- 前后端共同评审API设计
- API变更及时通知相关人员
- 提供Mock数据用于前端开发

### 3.2 接口联调流程
```
1. 后端完成功能开发
   ↓
2. 后端编写单元测试
   ↓
3. 后端提交代码并部署测试环境
   ↓
4. 前端根据API文档开发功能
   ↓
5. 前后端进行接口联调
   ↓
6. 发现问题及时沟通解决
   ↓
7. 联调通过后进入下一环节
```

### 3.3 Mock数据规范
```javascript
// 用户登录Mock数据
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "tokenType": "Bearer",
  "expiresIn": 86400,
  "user": {
    "id": 1,
    "username": "testuser",
    "email": "<EMAIL>"
  }
}

// 节日列表Mock数据
[
  {
    "id": 1,
    "name": "春节",
    "date": "2025-01-29",
    "holidayType": "传统节日",
    "description": "中国最重要的传统节日",
    "isRecurring": true,
    "createdAt": "2025-08-20T10:30:00Z"
  }
]
```

## 4. 代码管理规范

### 4.1 Git分支策略
```
main/master     # 生产环境分支
├── develop     # 开发环境分支
│   ├── feature/user-management    # 用户管理功能分支
│   ├── feature/holiday-management # 节日管理功能分支
│   └── feature/friend-management  # 好友管理功能分支
├── release/v1.0.0  # 发布版本分支
└── hotfix/bug-fix  # 紧急修复分支
```

### 4.2 提交规范
- **提交格式**: `<type>(<scope>): <subject>`
- **类型说明**:
  - feat: 新功能
  - fix: 修复bug
  - docs: 文档更新
  - style: 代码格式调整
  - refactor: 代码重构
  - test: 测试相关
  - chore: 构建过程或辅助工具的变动

### 4.3 代码审查流程
```
1. 开发者完成功能开发
   ↓
2. 提交Pull Request
   ↓
3. 指定代码审查人员
   ↓
4. 审查人员检查代码质量
   ↓
5. 提出修改建议
   ↓
6. 开发者修改后重新提交
   ↓
7. 审查通过后合并到主分支
```

## 5. 前端开发规范

### 5.1 项目结构
```
src/
├── assets/          # 静态资源文件
├── components/      # 公共组件
│   ├── layout/      # 布局组件
│   ├── form/        # 表单组件
│   └── ui/          # UI组件
├── views/           # 页面组件
│   ├── auth/        # 认证相关页面
│   ├── user/        # 用户相关页面
│   ├── holiday/     # 节日相关页面
│   ├── friend/      # 好友相关页面
│   └── template/    # 模板相关页面
├── services/        # API服务
├── stores/          # 状态管理
├── utils/           # 工具函数
├── router/          # 路由配置
└── types/           # TypeScript类型定义
```

### 5.2 组件开发规范
```vue
<!-- UserCard.vue -->
<template>
  <div class="user-card">
    <img :src="user.avatarUrl" :alt="user.username" class="avatar" />
    <div class="user-info">
      <h3 class="username">{{ user.username }}</h3>
      <p class="email">{{ user.email }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'

interface User {
  id: number
  username: string
  email: string
  avatarUrl: string
}

const props = defineProps<{
  user: User
}>()
</script>

<style scoped>
.user-card {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e5e5e5;
  border-radius: 0.5rem;
}

.avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  margin-right: 1rem;
}

.username {
  font-weight: 600;
  margin: 0 0 0.25rem 0;
}

.email {
  color: #666;
  margin: 0;
}
</style>
```

### 5.3 API服务封装
```typescript
// services/api.ts
import axios, { AxiosInstance, AxiosRequestConfig } from 'axios'

class ApiService {
  private axios: AxiosInstance

  constructor() {
    this.axios = axios.create({
      baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8080/v1/api',
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // 请求拦截器
    this.axios.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('accessToken')
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.axios.interceptors.response.use(
      (response) => {
        return response.data
      },
      (error) => {
        if (error.response?.status === 401) {
          // 处理未授权错误
          localStorage.removeItem('accessToken')
          window.location.href = '/login'
        }
        return Promise.reject(error)
      }
    )
  }

  public async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axios.get<T>(url, config)
    return response.data
  }

  public async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axios.post<T>(url, data, config)
    return response.data
  }

  public async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axios.put<T>(url, data, config)
    return response.data
  }

  public async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.axios.delete<T>(url, config)
    return response.data
  }
}

export default new ApiService()
```

## 6. 后端开发规范

### 6.1 项目结构
```
src/main/java/com/myproject/
├── controller/      # 控制器层
├── service/          # 服务层
├── repository/      # 数据访问层
├── entity/          # 实体类
├── dto/            # 数据传输对象
├── security/       # 安全配置
├── config/         # 配置类
├── exception/      # 异常处理
└── util/           # 工具类
```

### 6.2 控制器规范
```java
// UserController.java
@RestController
@RequestMapping("/v1/api/users")
@CrossOrigin(origins = "*", maxAge = 3600)
@Slf4j
public class UserController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    public ResponseEntity<UserDto> getCurrentUser(Authentication authentication) {
        try {
            UserDto user = userService.getCurrentUser(authentication);
            return ResponseEntity.ok(user);
        } catch (Exception e) {
            log.error("获取当前用户信息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 更新用户信息
     */
    @PutMapping("/me")
    public ResponseEntity<UserDto> updateUser(
            @Valid @RequestBody UpdateUserRequest request,
            Authentication authentication) {
        try {
            UserDto updatedUser = userService.updateUser(request, authentication);
            return ResponseEntity.ok(updatedUser);
        } catch (UserNotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (EmailAlreadyExistsException e) {
            return ResponseEntity.badRequest()
                .body(new ErrorResponse("邮箱已被使用"));
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ErrorResponse("服务器内部错误"));
        }
    }
}
```

### 6.3 服务层规范
```java
// UserService.java
@Service
@Transactional
@Slf4j
public class UserService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    /**
     * 获取当前用户信息
     */
    public UserDto getCurrentUser(Authentication authentication) {
        String email = authentication.getName();
        User user = userRepository.findByEmail(email)
            .orElseThrow(() -> new UserNotFoundException("用户不存在"));
        return UserMapper.toDto(user);
    }
    
    /**
     * 更新用户信息
     */
    public UserDto updateUser(UpdateUserRequest request, Authentication authentication) {
        String email = authentication.getName();
        User user = userRepository.findByEmail(email)
            .orElseThrow(() -> new UserNotFoundException("用户不存在"));
        
        // 检查邮箱是否已被其他用户使用
        if (request.getEmail() != null && 
            !request.getEmail().equals(user.getEmail()) &&
            userRepository.existsByEmail(request.getEmail())) {
            throw new EmailAlreadyExistsException("邮箱已被使用");
        }
        
        // 更新用户信息
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setAvatarUrl(request.getAvatarUrl());
        user.setUpdatedAt(LocalDateTime.now());
        
        User savedUser = userRepository.save(user);
        return UserMapper.toDto(savedUser);
    }
}
```

## 7. 测试协作规范

### 7.1 测试分工
- **后端**: 负责单元测试和集成测试
- **前端**: 负责组件测试和端到端测试
- **共同**: 参与系统测试和用户验收测试

### 7.2 测试覆盖要求
- **单元测试**: 80%以上代码覆盖率
- **集成测试**: 核心业务流程100%覆盖
- **端到端测试**: 主要用户场景100%覆盖

### 7.3 测试环境管理
- **开发环境**: 本地Docker容器
- **测试环境**: 云服务器测试实例
- **预发布环境**: 与生产环境相同配置
- **生产环境**: 正式线上环境

## 8. 沟通协作规范

### 8.1 日常沟通
- **即时通讯**: Slack/钉钉/微信
- **视频会议**: 腾讯会议/Zoom
- **文档协作**: Google Docs/石墨文档

### 8.2 问题反馈
- **紧急问题**: 立即电话或语音沟通
- **一般问题**: 在即时通讯工具中提出
- **技术难题**: 组织技术讨论会

### 8.3 知识共享
- **技术分享**: 每周五下午技术分享会
- **文档更新**: 及时更新项目文档
- **经验总结**: 迭代结束后进行经验总结

## 9. 质量保证规范

### 9.1 代码质量检查
- **静态代码分析**: SonarQube/ESLint
- **代码复杂度**: 控制在合理范围内
- **重复代码**: 消除重复代码片段

### 9.2 性能监控
- **响应时间**: API响应时间 < 500ms
- **并发处理**: 支持1000并发用户
- **资源使用**: CPU < 80%, 内存 < 80%

### 9.3 安全检查
- **漏洞扫描**: 定期进行安全漏洞扫描
- **权限控制**: 严格的权限验证机制
- **数据加密**: 敏感数据加密存储

## 10. 部署协作规范

### 10.1 部署流程
```
1. 代码合并到主分支
   ↓
2. 自动构建Docker镜像
   ↓
3. 推送到镜像仓库
   ↓
4. 自动化部署到测试环境
   ↓
5. 测试通过后部署到生产环境
   ↓
6. 健康检查和监控
```

### 10.2 回滚机制
- **自动回滚**: 部署失败自动回滚到上一版本
- **手动回滚**: 支持手动选择版本回滚
- **数据备份**: 部署前自动备份数据库

### 10.3 监控告警
- **应用监控**: 接口调用成功率、响应时间
- **系统监控**: CPU、内存、磁盘使用率
- **业务监控**: 用户活跃度、功能使用情况