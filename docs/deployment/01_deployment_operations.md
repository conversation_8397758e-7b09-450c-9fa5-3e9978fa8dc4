# 传统节日问候助手部署和运维规范

## 1. 部署架构设计

### 1.1 整体架构
```
┌─────────────────────────────────────────────────────────────────┐
│                        Load Balancer                          │
│                       (Nginx/Nginx Plus)                       │
└─────────────────────────────────────────────────────────────────┘
                                    │
        ┌───────────────────────────┼───────────────────────────┐
        │                           │                           │
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│   Application   │      │   Application   │      │   Application   │
│    Server 1     │      │    Server 2     │      │    Server 3     │
│   (Spring Boot) │      │   (Spring Boot) │      │   (Spring Boot) │
└─────────────────┘      └─────────────────┘      └─────────────────┘
        │                           │                           │
        └───────────────────────────┼───────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                          MySQL Master                           │
│                        (读写操作主库)                           │
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                          MySQL Slave                            │
│                         (只读操作从库)                           │
└─────────────────────────────────────────────────────────────────┘
                                    │
┌─────────────────────────────────────────────────────────────────┐
│                            Redis                                │
│                        (缓存和会话)                             │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 容器化部署架构
```
┌─────────────────────────────────────────────────────────────────┐
│                        Docker Swarm                             │
│                    (Orchestration Layer)                        │
└─────────────────────────────────────────────────────────────────┘
                                    │
        ┌───────────────────────────┼───────────────────────────┐
        │                           │                           │
┌─────────────────┐      ┌─────────────────┐      ┌─────────────────┐
│   Service A     │      │   Service B     │      │   Service C     │
│  (Web Servers)  │      │ (DB Services)   │      │ (Cache Services)│
│                 │      │                 │      │                 │
│  - Nginx        │      │  - MySQL        │      │  - Redis        │
│  - Spring Boot   │      │  - PostgreSQL   │      │  - Memcached    │
└─────────────────┘      └─────────────────┘      └─────────────────┘
```

## 2. 环境配置管理

### 2.1 环境分类
```
┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐
│    开发环境     │    测试环境     │    预发布环境   │    生产环境     │
│  (Development)  │   (Testing)     │  (Staging)      │  (Production)   │
├─────────────────┼─────────────────┼─────────────────┼─────────────────┤
│ 本地开发机器    │ 专用测试服务器  │ 准生产服务器    │ 正式线上服务器  │
│ 功能开发调试    │ 功能测试验证    │ 最终验证测试    │ 对外提供服务    │
│ 数据可随意修改  │ 测试数据隔离    │ 接近生产数据    │ 真实业务数据    │
│ 不影响他人工作  │ 多人共享测试    │ 严格变更控制    │ 高可用性保障    │
└─────────────────┴─────────────────┴─────────────────┴─────────────────┘
```

### 2.2 配置文件管理
```yaml
# application.yml (基础配置)
spring:
  application:
    name: festival-greeting-assistant
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:dev}

# application-dev.yml (开发环境)
spring:
  datasource:
    url: ****************************************
    username: dev_user
    password: dev_password
  redis:
    host: localhost
    port: 6379

# application-prod.yml (生产环境)
spring:
  datasource:
    url: jdbc:mysql://${DB_HOST}:${DB_PORT}/${DB_NAME}
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  redis:
    host: ${REDIS_HOST}
    port: ${REDIS_PORT}
```

### 2.3 环境变量配置
```bash
# .env (开发环境变量)
SPRING_PROFILES_ACTIVE=dev
DB_HOST=localhost
DB_PORT=3306
DB_NAME=festival_dev
DB_USERNAME=dev_user
DB_PASSWORD=dev_password
REDIS_HOST=localhost
REDIS_PORT=6379
JWT_SECRET=mySecretKey
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

## 3. Docker容器化部署

### 3.1 Dockerfile配置
```dockerfile
# Dockerfile
FROM openjdk:17-jdk-slim

# 设置工作目录
WORKDIR /app

# 复制jar文件
COPY target/festival-greeting-assistant-*.jar app.jar

# 暴露端口
EXPOSE 8080

# 设置环境变量
ENV SPRING_PROFILES_ACTIVE=docker
ENV JAVA_OPTS=""

# 启动应用
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -Djava.security.egd=file:/dev/./urandom -jar app.jar"]
```

### 3.2 Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  # 应用服务
  app:
    build: .
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=festival_prod
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - mysql
      - redis
    networks:
      - app-network

  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=festival_prod
      - MYSQL_USER=${DB_USERNAME}
      - MYSQL_PASSWORD=${DB_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    networks:
      - app-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - app
    networks:
      - app-network

volumes:
  mysql_data:
  redis_data:

networks:
  app-network:
    driver: bridge
```

### 3.3 生产环境Docker Compose
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    image: registry.example.com/festival-app:${VERSION:-latest}
    deploy:
      replicas: 3
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_NAME=${DB_NAME}
      - DB_USERNAME=${DB_USERNAME}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - JWT_SECRET=${JWT_SECRET}
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  mysql-master:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${DB_NAME}
      - MYSQL_USER=${DB_USERNAME}
      - MYSQL_PASSWORD=${DB_PASSWORD}
    volumes:
      - mysql_master_data:/var/lib/mysql
      - ./mysql/master.cnf:/etc/mysql/conf.d/master.cnf
    networks:
      - db-network
    command: --server-id=1 --log-bin=mysql-bin --binlog-format=row

  mysql-slave:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - MYSQL_DATABASE=${DB_NAME}
    volumes:
      - mysql_slave_data:/var/lib/mysql
      - ./mysql/slave.cnf:/etc/mysql/conf.d/slave.cnf
    networks:
      - db-network
    command: --server-id=2 --relay-log=relay-bin --read_only=1
    depends_on:
      - mysql-master

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    networks:
      - app-network
    depends_on:
      - app

volumes:
  mysql_master_data:
  mysql_slave_data:
  redis_data:

networks:
  app-network:
  db-network:
```

## 4. Kubernetes部署配置

### 4.1 Deployment配置
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: festival-app
  labels:
    app: festival-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: festival-app
  template:
    metadata:
      labels:
        app: festival-app
    spec:
      containers:
      - name: festival-app
        image: registry.example.com/festival-app:v1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: festival-config
              key: db.host
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: festival-config
              key: db.port
        - name: DB_NAME
          valueFrom:
            configMapKeyRef:
              name: festival-config
              key: db.name
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: festival-secrets
              key: db.username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: festival-secrets
              key: db.password
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
```

### 4.2 Service配置
```yaml
# k8s/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: festival-app-service
spec:
  selector:
    app: festival-app
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: festival-app-external
spec:
  selector:
    app: festival-app
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8080
  type: LoadBalancer
```

### 4.3 Ingress配置
```yaml
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: festival-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.festival.example.com
    secretName: festival-tls
  rules:
  - host: api.festival.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: festival-app-service
            port:
              number: 80
```

## 5. 数据库部署和管理

### 5.1 MySQL主从复制配置
```ini
# mysql/master.cnf
[mysqld]
server-id=1
log-bin=mysql-bin
binlog-format=row
binlog-do-db=festival_prod
expire_logs_days=7
```

```ini
# mysql/slave.cnf
[mysqld]
server-id=2
relay-log=relay-bin
read_only=1
replicate-do-db=festival_prod
```

### 5.2 数据库备份策略
```bash
#!/bin/bash
# backup.sh
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="festival_backup_${DATE}.sql"

# 创建备份目录
mkdir -p ${BACKUP_DIR}

# 执行备份
mysqldump -h ${DB_HOST} -P ${DB_PORT} -u ${DB_USERNAME} -p${DB_PASSWORD} \
  ${DB_NAME} > ${BACKUP_DIR}/${BACKUP_FILE}

# 压缩备份文件
gzip ${BACKUP_DIR}/${BACKUP_FILE}

# 删除7天前的备份
find ${BACKUP_DIR} -name "festival_backup_*.sql.gz" -mtime +7 -delete

# 上传到云存储 (可选)
# aws s3 cp ${BACKUP_DIR}/${BACKUP_FILE}.gz s3://backup-bucket/festival/
```

### 5.3 数据库监控
```sql
-- 监控查询性能
SHOW PROCESSLIST;
SHOW STATUS LIKE 'Threads_connected';
SHOW STATUS LIKE 'Questions';
SHOW STATUS LIKE 'Slow_queries';

-- 监控表大小
SELECT 
    table_name AS `Table`,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) `MB`
FROM information_schema.tables 
WHERE table_schema = 'festival_prod'
ORDER BY (data_length + index_length) DESC;
```

## 6. 监控和告警

### 6.1 Prometheus配置
```yaml
# prometheus/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'festival-app'
    static_configs:
      - targets: ['app:8080']
    metrics_path: '/actuator/prometheus'

  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
```

### 6.2 Grafana监控面板
```json
{
  "dashboard": {
    "title": "Festival Greeting Assistant Monitoring",
    "panels": [
      {
        "title": "Application Uptime",
        "type": "graph",
        "targets": [
          {
            "expr": "up{job='festival-app'}",
            "legendFormat": "Application Status"
          }
        ]
      },
      {
        "title": "API Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, sum(rate(http_server_requests_seconds_bucket[5m])) by (le))",
            "legendFormat": "95th Percentile"
          }
        ]
      },
      {
        "title": "Database Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "mysql_global_status_threads_connected",
            "legendFormat": "Connected Threads"
          }
        ]
      }
    ]
  }
}
```

### 6.3 告警规则
```yaml
# alertmanager/alerts.yml
groups:
  - name: festival-alerts
    rules:
      - alert: HighErrorRate
        expr: rate(http_server_requests_seconds_count{status=~"5.."}[5m]) > 0.05
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "High error rate on festival app"
          description: "{{ $value }}% of requests are failing"

      - alert: HighLatency
        expr: histogram_quantile(0.95, sum(rate(http_server_requests_seconds_bucket[5m])) by (le)) > 1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High latency on festival app"
          description: "95th percentile latency is above 1 second"

      - alert: DatabaseDown
        expr: mysql_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "MySQL database is down"
          description: "MySQL database is not responding"
```

## 7. 日志管理

### 7.1 应用日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <springProfile name="default">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        
        <root level="INFO">
            <appender-ref ref="CONSOLE" />
        </root>
    </springProfile>
    
    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/application.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>logs/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <maxFileSize>100MB</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
                <maxHistory>30</maxHistory>
                <totalSizeCap>10GB</totalSizeCap>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        
        <root level="INFO">
            <appender-ref ref="FILE" />
        </root>
    </springProfile>
</configuration>
```

### 7.2 ELK日志收集
```yaml
# docker-compose.logging.yml
version: '3.8'

services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.0.0
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms1g -Xmx1g
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - es_data:/usr/share/elasticsearch/data

  logstash:
    image: docker.elastic.co/logstash/logstash:8.0.0
    volumes:
      - ./logstash/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    ports:
      - "5044:5044"
    depends_on:
      - elasticsearch

  kibana:
    image: docker.elastic.co/kibana/kibana:8.0.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=["http://elasticsearch:9200"]
    depends_on:
      - elasticsearch

volumes:
  es_data:
```

## 8. 安全运维

### 8.1 SSL证书管理
```bash
#!/bin/bash
# ssl-renew.sh
# 使用Let's Encrypt自动续期SSL证书

DOMAIN="api.festival.example.com"
EMAIL="<EMAIL>"

# 安装certbot (如果未安装)
if ! command -v certbot &> /dev/null; then
    apt-get update
    apt-get install -y certbot
fi

# 续期证书
certbot renew --quiet

# 重启Nginx加载新证书
systemctl reload nginx

# 发送续期通知
echo "SSL certificate renewed for ${DOMAIN}" | mail -s "SSL Certificate Renewal" ${EMAIL}
```

### 8.2 安全扫描
```bash
#!/bin/bash
# security-scan.sh
# 定期安全扫描脚本

# 扫描开放端口
nmap -p 1-65535 localhost > /tmp/nmap_scan.txt

# 检查系统更新
apt-get update
apt-get upgrade --dry-run > /tmp/apt_upgrade.txt

# 扫描恶意软件
clamscan -r /var/www > /tmp/clamav_scan.txt

# 检查SSH登录尝试
grep "Failed password" /var/log/auth.log | tail -20 > /tmp/ssh_failures.txt

# 发送安全报告
{
    echo "Security Scan Report - $(date)"
    echo "==========================="
    echo ""
    echo "Open Ports:"
    cat /tmp/nmap_scan.txt
    echo ""
    echo "SSH Failures:"
    cat /tmp/ssh_failures.txt
} | mail -s "Security Scan Report" <EMAIL>
```

## 9. 备份和灾难恢复

### 9.1 备份策略
```
备份类型      频率        保留期限      存储位置
─────────────────────────────────────────────────────
应用代码      每次部署     永久         Git仓库
数据库        每日         30天         云存储
配置文件      每周         永久         配置管理工具
日志文件      每日         90天         云存储
静态资源      每周         永久         云存储
```

### 9.2 灾难恢复计划
```bash
#!/bin/bash
# disaster-recovery.sh
# 灾难恢复脚本

RECOVERY_TARGET="production"
BACKUP_DATE=$(date +%Y%m%d)

echo "Starting disaster recovery for ${RECOVERY_TARGET}..."

# 1. 恢复数据库
echo "Restoring database..."
gunzip < /backup/mysql/festival_backup_${BACKUP_DATE}.sql.gz | \
mysql -h ${DB_HOST} -P ${DB_PORT} -u ${DB_USERNAME} -p${DB_PASSWORD} ${DB_NAME}

# 2. 恢复应用代码
echo "Deploying application..."
kubectl set image deployment/festival-app festival-app=registry.example.com/festival-app:latest

# 3. 恢复配置文件
echo "Restoring configurations..."
kubectl apply -f k8s/

# 4. 验证恢复
echo "Verifying recovery..."
kubectl rollout status deployment/festival-app

echo "Disaster recovery completed!"
```

## 10. 性能优化

### 10.1 应用性能调优
```bash
# JVM调优参数
JAVA_OPTS="-Xms512m -Xmx2g \
           -XX:+UseG1GC \
           -XX:MaxGCPauseMillis=200 \
           -XX:+UnlockExperimentalVMOptions \
           -XX:+UseStringDeduplication \
           -XX:+HeapDumpOnOutOfMemoryError \
           -XX:HeapDumpPath=/tmp/heapdump.hprof"
```

### 10.2 数据库性能优化
```sql
-- 创建必要的索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_holidays_date ON holidays(date);
CREATE INDEX idx_friends_user_id ON friends(user_id);
CREATE INDEX idx_friends_email ON friends(email);

-- 优化查询语句
EXPLAIN SELECT u.username, h.name, h.date 
FROM users u 
JOIN user_greetings ug ON u.id = ug.user_id 
JOIN holidays h ON ug.holiday_id = h.id 
WHERE u.email = '<EMAIL>';
```

### 10.3 缓存策略
```java
// Redis缓存配置
@Configuration
@EnableCaching
public class RedisConfig {
    
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        return template;
    }
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofHours(1))
            .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        return RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(config)
            .build();
    }
}
```

## 11. 自动化运维

### 11.1 CI/CD流水线
```yaml
# .github/workflows/deploy.yml
name: Deploy Pipeline
on:
  push:
    branches: [ main, release/* ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        
      - name: Setup Java
        uses: actions/setup-java@v2
        with:
          java-version: '17'
          
      - name: Build application
        run: mvn clean package -DskipTests
        
      - name: Build Docker image
        run: |
          docker build -t registry.example.com/festival-app:${GITHUB_SHA} .
          
      - name: Push to registry
        run: |
          docker login registry.example.com -u ${{ secrets.REGISTRY_USERNAME }} -p ${{ secrets.REGISTRY_PASSWORD }}
          docker push registry.example.com/festival-app:${GITHUB_SHA}

  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to staging
        run: |
          ssh deploy@staging-server "cd /opt/festival && \
            docker-compose pull && \
            docker-compose up -d"
            
      - name: Deploy to production
        if: github.ref == 'refs/heads/main'
        run: |
          ssh deploy@prod-server "cd /opt/festival && \
            docker-compose pull && \
            docker-compose up -d"
```

### 11.2 健康检查脚本
```bash
#!/bin/bash
# health-check.sh
# 应用健康检查脚本

HEALTH_ENDPOINT="http://localhost:8080/actuator/health"
MAX_RETRIES=3
RETRY_DELAY=5

for i in $(seq 1 $MAX_RETRIES); do
    if curl -f -s $HEALTH_ENDPOINT > /dev/null; then
        echo "Application is healthy"
        exit 0
    else
        echo "Health check failed, retry $i/$MAX_RETRIES"
        sleep $RETRY_DELAY
    fi
done

echo "Application is unhealthy after $MAX_RETRIES attempts"
exit 1
```

通过以上全面的部署和运维规范，我们可以确保传统节日问候助手项目在各种环境下都能稳定、安全、高效地运行，同时具备良好的可维护性和扩展性。