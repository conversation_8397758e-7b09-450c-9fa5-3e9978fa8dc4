# 传统节日问候助手数据库设计规范

## 1. 设计原则

### 1.1 规范化原则
- 遵循数据库第三范式(3NF)
- 消除数据冗余
- 确保数据一致性
- 建立合适的索引策略

### 1.2 命名规范
- **表名**: 小写 + 下划线分隔 (users, greeting_templates)
- **字段名**: 小写 + 下划线分隔 (user_id, created_at)
- **主键**: id (BIGINT AUTO_INCREMENT)
- **外键**: 相关表名_id (user_id, template_id)
- **索引**: idx_表名_字段名 (idx_users_email)

### 1.3 数据类型规范
- **整数**: BIGINT (64位)
- **字符串**: VARCHAR(N) 或 TEXT
- **日期时间**: TIMESTAMP
- **布尔值**: TINYINT(1) 或 BOOLEAN
- **浮点数**: DECIMAL(M,D)

## 2. 核心表结构设计

### 2.1 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱地址',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希值',
    reminder_time TIME COMMENT '默认提醒时间',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    phone VARCHAR(20) COMMENT '手机号码',
    is_active BOOLEAN DEFAULT TRUE COMMENT '账户是否激活',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    failed_login_attempts INT DEFAULT 0 COMMENT '登录失败次数',
    locked_until TIMESTAMP NULL COMMENT '账户锁定截止时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_users_email (email),
    INDEX idx_users_username (username),
    INDEX idx_users_last_login (last_login_at)
) COMMENT='用户信息表';
```

### 2.2 角色表 (roles)
```sql
CREATE TABLE roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '角色ID',
    name VARCHAR(50) UNIQUE NOT NULL COMMENT '角色名称',
    description TEXT COMMENT '角色描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_roles_name (name)
) COMMENT='角色信息表';
```

### 2.3 用户角色关联表 (user_roles)
```sql
CREATE TABLE user_roles (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    role_id BIGINT NOT NULL COMMENT '角色ID',
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    
    UNIQUE KEY uk_user_role (user_id, role_id),
    INDEX idx_user_roles_user_id (user_id),
    INDEX idx_user_roles_role_id (role_id)
) COMMENT='用户角色关联表';
```

### 2.4 节日表 (holidays)
```sql
CREATE TABLE holidays (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '节日ID',
    name VARCHAR(255) NOT NULL COMMENT '节日名称',
    date DATE NOT NULL COMMENT '节日日期',
    holiday_type VARCHAR(100) COMMENT '节日类型',
    description TEXT COMMENT '节日描述',
    is_recurring BOOLEAN DEFAULT FALSE COMMENT '是否循环节日',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_by BIGINT COMMENT '创建者ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_holidays_date (date),
    INDEX idx_holidays_type (holiday_type),
    INDEX idx_holidays_name (name),
    INDEX idx_holidays_recurring (is_recurring)
) COMMENT='节日信息表';
```

### 2.5 好友表 (friends)
```sql
CREATE TABLE friends (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '好友ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    friend_id BIGINT COMMENT '好友用户ID（如果是系统用户）',
    nickname VARCHAR(100) COMMENT '好友昵称',
    email VARCHAR(100) COMMENT '好友邮箱',
    phone VARCHAR(20) COMMENT '好友电话',
    avatar_url VARCHAR(255) COMMENT '好友头像URL',
    notes TEXT COMMENT '备注信息',
    is_favorite BOOLEAN DEFAULT FALSE COMMENT '是否收藏',
    group_id BIGINT COMMENT '分组ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (friend_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (group_id) REFERENCES friend_groups(id) ON DELETE SET NULL,
    
    INDEX idx_friends_user_id (user_id),
    INDEX idx_friends_friend_id (friend_id),
    INDEX idx_friends_email (email),
    INDEX idx_friends_phone (phone),
    INDEX idx_friends_nickname (nickname),
    INDEX idx_friends_is_favorite (is_favorite),
    INDEX idx_friends_group_id (group_id)
) COMMENT='好友信息表';
```

### 2.6 好友分组表 (friend_groups)
```sql
CREATE TABLE friend_groups (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '分组ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    name VARCHAR(100) NOT NULL COMMENT '分组名称',
    description TEXT COMMENT '分组描述',
    color VARCHAR(7) COMMENT '分组颜色(#RRGGBB)',
    sort_order INTEGER DEFAULT 0 COMMENT '排序序号',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_friend_groups_user_id (user_id),
    INDEX idx_friend_groups_name (name)
) COMMENT='好友分组表';
```

### 2.7 问候模板表 (greeting_templates)
```sql
CREATE TABLE greeting_templates (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '模板ID',
    name VARCHAR(255) NOT NULL COMMENT '模板名称',
    content TEXT NOT NULL COMMENT '模板内容',
    style_type VARCHAR(100) COMMENT '样式类型',
    category VARCHAR(100) COMMENT '模板分类',
    holiday_id BIGINT COMMENT '适用节日ID',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    language VARCHAR(10) DEFAULT 'zh' COMMENT '语言',
    created_by BIGINT COMMENT '创建者ID',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (holiday_id) REFERENCES holidays(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_greeting_templates_category (category),
    INDEX idx_greeting_templates_style_type (style_type),
    INDEX idx_greeting_templates_holiday_id (holiday_id),
    INDEX idx_greeting_templates_language (language),
    INDEX idx_greeting_templates_is_active (is_active),
    INDEX idx_greeting_templates_created_by (created_by),
    INDEX idx_greeting_templates_usage_count (usage_count)
) COMMENT='问候模板表';
```

### 2.8 用户问候设置表 (user_greetings)
```sql
CREATE TABLE user_greetings (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '设置ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    friend_id BIGINT NOT NULL COMMENT '好友ID',
    holiday_id BIGINT NOT NULL COMMENT '节日ID',
    template_id BIGINT COMMENT '模板ID',
    custom_message TEXT COMMENT '自定义消息',
    send_time TIME COMMENT '发送时间',
    is_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    last_sent_at TIMESTAMP NULL COMMENT '上次发送时间',
    next_send_at TIMESTAMP NULL COMMENT '下次发送时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (friend_id) REFERENCES friends(id) ON DELETE CASCADE,
    FOREIGN KEY (holiday_id) REFERENCES holidays(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES greeting_templates(id) ON DELETE SET NULL,
    
    UNIQUE KEY uk_user_friend_holiday (user_id, friend_id, holiday_id),
    INDEX idx_user_greetings_user_id (user_id),
    INDEX idx_user_greetings_friend_id (friend_id),
    INDEX idx_user_greetings_holiday_id (holiday_id),
    INDEX idx_user_greetings_template_id (template_id),
    INDEX idx_user_greetings_next_send (next_send_at),
    INDEX idx_user_greetings_is_enabled (is_enabled)
) COMMENT='用户问候设置表';
```

### 2.9 邮件发送记录表 (email_logs)
```sql
CREATE TABLE email_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    friend_id BIGINT NOT NULL COMMENT '好友ID',
    greeting_id BIGINT NOT NULL COMMENT '问候设置ID',
    subject VARCHAR(255) NOT NULL COMMENT '邮件主题',
    content TEXT NOT NULL COMMENT '邮件内容',
    status ENUM('pending', 'sent', 'failed', 'retrying') DEFAULT 'pending' COMMENT '发送状态',
    error_message TEXT COMMENT '错误信息',
    sent_at TIMESTAMP NULL COMMENT '发送时间',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (friend_id) REFERENCES friends(id) ON DELETE CASCADE,
    FOREIGN KEY (greeting_id) REFERENCES user_greetings(id) ON DELETE CASCADE,
    
    INDEX idx_email_logs_user_id (user_id),
    INDEX idx_email_logs_friend_id (friend_id),
    INDEX idx_email_logs_status (status),
    INDEX idx_email_logs_sent_at (sent_at),
    INDEX idx_email_logs_greeting_id (greeting_id)
) COMMENT='邮件发送记录表';
```

### 2.10 系统通知表 (notifications)
```sql
CREATE TABLE notifications (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '通知ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    title VARCHAR(255) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    type ENUM('system', 'warning', 'info', 'success', 'error') DEFAULT 'info' COMMENT '通知类型',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    read_at TIMESTAMP NULL COMMENT '阅读时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    INDEX idx_notifications_user_id (user_id),
    INDEX idx_notifications_type (type),
    INDEX idx_notifications_is_read (is_read),
    INDEX idx_notifications_created_at (created_at)
) COMMENT='系统通知表';
```

## 3. 数据完整性约束

### 3.1 主键约束
所有表都必须有主键，使用BIGINT类型并设置AUTO_INCREMENT。

### 3.2 外键约束
建立合理的外键关系，确保数据引用完整性。

### 3.3 唯一性约束
对需要唯一性的字段设置UNIQUE约束。

### 3.4 非空约束
对必需字段设置NOT NULL约束。

## 4. 索引策略

### 4.1 主键索引
每张表的主键自动创建主键索引。

### 4.2 外键索引
所有外键字段自动创建索引。

### 4.3 业务索引
根据查询需求创建以下索引：
- 经常用于WHERE条件的字段
- 经常用于ORDER BY的字段
- 经常用于JOIN的字段
- 组合索引（复合查询场景）

## 5. 数据初始化

### 5.1 默认角色
```sql
INSERT INTO roles (name, description) VALUES 
('admin', '系统管理员'),
('user', '普通用户');
```

### 5.2 默认管理员用户
```sql
INSERT INTO users (username, email, password_hash, is_active) VALUES 
('admin', '<EMAIL>', '$2a$10$8K1p/a0dhrxiowP.dnkgNORTWgdEDHn5L2/xjpEWuC.QQv4rKO9jO', TRUE);
```

### 5.3 默认角色分配
```sql
INSERT INTO user_roles (user_id, role_id) VALUES 
(1, 1); -- admin用户分配admin角色
```

### 5.4 示例节日数据
```sql
INSERT INTO holidays (name, date, holiday_type, description, is_recurring) VALUES
('春节', '2025-01-29', '传统节日', '中国最重要的传统节日', TRUE),
('元宵节', '2025-02-12', '传统节日', '春节之后的第一个重要节日', TRUE),
('清明节', '2025-04-04', '传统节日', '祭祖扫墓的传统节日', TRUE),
('端午节', '2025-05-31', '传统节日', '纪念屈原的传统节日', TRUE),
('七夕节', '2025-08-20', '传统节日', '中国传统情人节', TRUE),
('中秋节', '2025-10-06', '传统节日', '团圆赏月的传统节日', TRUE),
('重阳节', '2025-10-21', '传统节日', '敬老爱老的传统节日', TRUE),
('元旦', '2025-01-01', '法定假日', '新年的第一天', TRUE),
('劳动节', '2025-05-01', '法定假日', '国际劳动节', TRUE),
('国庆节', '2025-10-01', '法定假日', '中华人民共和国国庆节', TRUE);
```

## 6. 数据库维护规范

### 6.1 备份策略
- 每日全量备份
- 每小时增量备份
- 备份数据异地存储
- 定期备份恢复演练

### 6.2 性能监控
- 慢查询日志分析
- 数据库连接池监控
- 索引使用情况分析
- 存储空间监控

### 6.3 安全规范
- 定期更新数据库补丁
- 严格的访问权限控制
- 敏感数据加密存储
- SQL注入防护

### 6.4 扩展规划
- 读写分离
- 分库分表
- 主从复制
- 数据归档策略