# 传统节日问候助手项目核心文档清单

## 项目基础文档

### 1. 项目概述
- **文件**: `/Users/<USER>/MyProject/README.md`
- **内容**: 项目简介、技术栈、快速开始指南、API端点列表

### 2. 需求分析
- **文件**: `/Users/<USER>/MyProject/docs/development/02_requirements_analysis.md`
- **内容**: 功能需求、非功能需求、用户角色分析、业务流程

### 3. 系统架构设计
- **文件**: `/Users/<USER>/MyProject/docs/architecture/01_system_architecture.md`
- **内容**: 技术架构图、模块划分、数据流设计、安全设计

### 4. 数据库设计
- **文件**: `/Users/<USER>/MyProject/docs/database/01_database_design.md`
- **内容**: 表结构设计、字段说明、索引策略、关系图

### 5. API接口规范
- **文件**: `/Users/<USER>/MyProject/docs/api/01_api_specification.md`
- **内容**: 所有RESTful API接口详细规范、请求响应格式

## 开发规范文档

### 6. 项目开发规范
- **文件**: `/Users/<USER>/MyProject/docs/development/01_project_guidelines.md`
- **内容**: 开发流程、代码规范、分支策略、提交规范

### 7. 协作开发规范
- **文件**: `/Users/<USER>/MyProject/docs/development/03_collaboration_guidelines.md`
- **内容**: 前后端协作流程、接口联调规范、Mock数据规范

## 测试规范文档

### 8. 测试策略
- **文件**: `/Users/<USER>/MyProject/docs/testing/01_testing_strategy.md`
- **内容**: 测试类型、测试工具、覆盖率要求、自动化测试

## 部署运维文档

### 9. 部署规范
- **文件**: `/Users/<USER>/MyProject/docs/deployment/01_deployment_operations.md`
- **内容**: 部署架构、环境配置、容器化部署、监控告警

## 职位工作要求

### 10. 后端开发职位要求
**核心职责**:
- 实现用户认证系统 (注册、登录、JWT)
- 开发节日管理API (增删改查)
- 实现好友管理功能 (好友信息、分组管理)
- 开发问候模板系统 (模板管理、分类)
- 实现邮件提醒服务 (定时任务、邮件发送)
- 数据库设计和优化
- API文档编写和维护
- 单元测试和集成测试编写

**技术要求**:
- 精通Java和Spring Boot框架
- 熟悉MySQL数据库设计和优化
- 掌握Redis缓存使用
- 熟悉JWT安全认证机制
- 了解邮件发送服务集成
- 掌握JUnit/Mockito测试框架

### 11. 前端开发职位要求
**核心职责**:
- 实现用户界面 (登录注册、个人中心)
- 开发节日展示页面 (节日列表、详情)
- 实现好友管理界面 (好友列表、添加编辑)
- 开发问候模板页面 (模板展示、编辑)
- 实现邮件记录页面 (发送历史、状态)
- 响应式设计适配移动端
- 与后端API对接联调
- 前端组件库开发和维护

**技术要求**:
- 精通Vue 3和TypeScript
- 熟悉Vite构建工具
- 掌握Tailwind CSS样式框架
- 熟悉Axios HTTP客户端
- 了解Pinia状态管理
- 掌握Vue Router路由管理
- 熟悉Vitest/Cypress测试工具

### 12. 测试工程师职位要求
**核心职责**:
- 制定测试计划和测试用例
- 执行功能测试和回归测试
- 进行性能测试和压力测试
- 实施自动化测试
- 编写测试报告和缺陷分析
- 参与代码审查和质量评估
- 建立测试环境和数据准备

**技术要求**:
- 掌握JUnit 5和Mockito单元测试
- 熟悉Spring Boot Test集成测试
- 了解Vitest前端测试框架
- 掌握Cypress端到端测试
- 熟悉Postman API测试工具
- 了解JMeter性能测试工具
- 掌握测试管理工具使用

### 13. DevOps工程师职位要求
**核心职责**:
- 设计和维护Docker容器化部署
- 配置Nginx反向代理和负载均衡
- 建立CI/CD流水线
- 实施监控和告警系统
- 管理生产环境和备份恢复
- 优化系统性能和安全性
- 编写运维文档和操作手册

**技术要求**:
- 精通Docker和Docker Compose
- 熟悉Nginx配置和优化
- 掌握CI/CD工具(GitHub Actions)
- 了解Prometheus和Grafana监控
- 熟悉Linux系统管理和Shell脚本
- 掌握云服务部署和管理
- 了解安全加固和漏洞扫描

## 核心配置文件

### 14. 环境配置
- **文件**: `/Users/<USER>/MyProject/.env` 和 `/Users/<USER>/MyProject/.env.prod`
- **内容**: 开发和生产环境变量配置

### 15. Docker配置
- **文件**: `/Users/<USER>/MyProject/docker-compose.yml`
- **内容**: 开发环境容器编排配置

### 16. 启动脚本
- **文件**: `/Users/<USER>/MyProject/start.sh`
- **内容**: 项目一键启动脚本

## 精简后的核心文档清单

为方便您重新开始项目，以下是必须的核心文档：

1. **README.md** - 项目主说明和快速开始指南
2. **docs/architecture/01_system_architecture.md** - 系统架构设计
3. **docs/database/01_database_design.md** - 数据库设计
4. **docs/api/01_api_specification.md** - API接口规范
5. **docs/development/02_requirements_analysis.md** - 需求分析
6. **docs/testing/01_testing_strategy.md** - 测试策略
7. **docs/deployment/01_deployment_operations.md** - 部署规范
8. **职位工作要求** (见上述第10-13项)

这些文档包含了重新开发项目所需的所有技术信息，已去除人员相关信息，只保留职位职责和技能要求，便于您分配任务给不同的Agent。