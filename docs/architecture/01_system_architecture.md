# 传统节日问候助手系统架构设计文档

## 1. 系统架构概述

### 1.1 架构模式
采用前后端分离的微服务架构模式，后端基于Spring Boot构建RESTful API服务，前端基于Vue 3构建单页应用。

### 1.2 架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Browser   │    │   Mobile App    │    │   Third Party   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────────┐
                    │    API Gateway      │
                    │    (Nginx)          │
                    └─────────────────────┘
                                 │
        ┌────────────────────────┼────────────────────────┐
        │                        │                        │
┌───────────────┐      ┌───────────────┐      ┌───────────────┐
│  Frontend     │      │  Backend      │      │  Database     │
│  (Vue 3)      │◄────►│  (Spring Boot)│◄────►│  (MySQL)      │
└───────────────┘      └───────────────┘      └───────────────┘
                                │                        │
                       ┌───────────────┐      ┌───────────────┐
                       │   Cache       │      │   Email       │
                       │   (Redis)     │      │   Service     │
                       └───────────────┘      └───────────────┘
```

## 2. 技术选型

### 2.1 后端技术栈

#### 2.1.1 核心框架
- **Spring Boot 3.x**: 作为主要开发框架，提供自动配置和快速开发能力
- **Spring Security**: 提供安全认证和授权机制
- **Spring Data JPA**: 提供数据访问层抽象
- **Spring Mail**: 提供邮件发送功能

#### 2.1.2 数据库相关
- **MySQL 8.0**: 关系型数据库，存储核心业务数据
- **Redis**: 缓存数据库，存储会话信息和临时数据
- **HikariCP**: 数据库连接池，提供高性能连接管理

#### 2.1.3 安全认证
- **JWT (JSON Web Token)**: 无状态认证机制
- **BCrypt**: 密码加密算法
- **OAuth2**: 第三方登录支持（扩展功能）

#### 2.1.4 API文档
- **Springfox Swagger**: 自动生成API文档
- **OpenAPI 3.0**: API规范标准

#### 2.1.5 日志和监控
- **Logback**: 日志框架
- **SLF4J**: 日志门面
- **Micrometer**: 应用监控指标收集
- **Spring Boot Actuator**: 应用健康检查

#### 2.1.6 构建和部署
- **Maven**: 项目构建工具
- **Docker**: 容器化部署
- **Docker Compose**: 多容器编排

### 2.2 前端技术栈

#### 2.2.1 核心框架
- **Vue 3**: 渐进式JavaScript框架
- **TypeScript**: 静态类型检查
- **Vite**: 构建工具，提供快速开发体验
- **Vue Router**: 路由管理
- **Pinia**: 状态管理

#### 2.2.2 UI组件库
- **Tailwind CSS**: 实用优先的CSS框架
- **Headless UI**: 无样式UI组件
- **Heroicons**: SVG图标库

#### 2.2.3 网络请求
- **Axios**: HTTP客户端
- **Fetch API**: 原生HTTP请求

#### 2.2.4 开发工具
- **ESLint**: 代码规范检查
- **Prettier**: 代码格式化
- **Vitest**: 单元测试框架
- **Cypress**: 端到端测试

### 2.3 基础设施

#### 2.3.1 服务器环境
- **操作系统**: Ubuntu 20.04 LTS
- **Web服务器**: Nginx
- **应用服务器**: 内嵌Tomcat (Spring Boot)
- **容器化**: Docker

#### 2.3.2 邮件服务
- **SMTP服务**: Gmail SMTP (开发阶段)
- **第三方服务**: SendGrid/Amazon SES (生产环境)

#### 2.3.3 监控和日志
- **日志收集**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **应用监控**: Prometheus + Grafana
- **错误追踪**: Sentry

## 3. 系统模块设计

### 3.1 后端模块划分

#### 3.1.1 认证授权模块 (auth-service)
**职责**: 处理用户注册、登录、权限验证
**主要功能**:
- 用户注册
- 用户登录
- JWT令牌生成和验证
- 密码重置
- 会话管理

#### 3.1.2 用户管理模块 (user-service)
**职责**: 管理用户基本信息和设置
**主要功能**:
- 用户信息管理
- 用户设置管理
- 用户偏好配置
- 账户安全设置

#### 3.1.3 节日管理模块 (holiday-service)
**职责**: 管理节日数据和相关信息
**主要功能**:
- 节日数据维护
- 节日查询
- 节日分类管理
- 节日提醒设置

#### 3.1.4 好友管理模块 (friend-service)
**职责**: 管理用户的好友联系信息
**主要功能**:
- 好友信息管理
- 好友分组
- 好友搜索
- 好友导入导出

#### 3.1.5 模板管理模块 (template-service)
**职责**: 管理问候模板
**主要功能**:
- 模板创建和编辑
- 模板分类
- 模板搜索
- 模板收藏

#### 3.1.6 邮件服务模块 (email-service)
**职责**: 处理邮件发送和管理
**主要功能**:
- 邮件模板生成
- 邮件发送
- 发送状态跟踪
- 发送失败重试

#### 3.1.7 通知服务模块 (notification-service)
**职责**: 处理系统通知和用户提醒
**主要功能**:
- 系统公告
- 用户提醒
- 通知推送
- 通知历史

### 3.2 前端模块划分

#### 3.2.1 公共组件模块
**职责**: 提供可复用的UI组件
**包含组件**:
- 导航栏
- 表单组件
- 表格组件
- 模态框
- 按钮组件

#### 3.2.2 认证模块
**职责**: 处理用户认证相关页面
**包含页面**:
- 登录页面
- 注册页面
- 忘记密码页面
- 重置密码页面

#### 3.2.3 用户中心模块
**职责**: 用户个人信息管理
**包含页面**:
- 个人资料页面
- 账户设置页面
- 安全设置页面
- 通知设置页面

#### 3.2.4 节日管理模块
**职责**: 节日信息展示和管理
**包含页面**:
- 节日列表页面
- 节日详情页面
- 节日提醒设置页面

#### 3.2.5 好友管理模块
**职责**: 好友信息管理
**包含页面**:
- 好友列表页面
- 好友详情页面
- 好友添加页面
- 好友分组管理页面

#### 3.2.6 模板管理模块
**职责**: 问候模板管理
**包含页面**:
- 模板列表页面
- 模板详情页面
- 模板创建页面
- 模板编辑页面

## 4. 数据流设计

### 4.1 用户注册流程
```
1. 前端发送注册请求
   ↓
2. 后端验证输入数据
   ↓
3. 检查邮箱是否已存在
   ↓
4. 密码加密存储
   ↓
5. 生成邮箱验证链接
   ↓
6. 发送验证邮件
   ↓
7. 返回注册成功响应
```

### 4.2 用户登录流程
```
1. 前端发送登录请求
   ↓
2. 后端验证用户名密码
   ↓
3. 生成JWT令牌
   ↓
4. 返回令牌和用户信息
   ↓
5. 前端存储令牌
   ↓
6. 后续请求携带令牌
```

### 4.3 邮件发送流程
```
1. 定时任务触发
   ↓
2. 查询待发送的提醒
   ↓
3. 生成个性化内容
   ↓
4. 调用邮件服务发送
   ↓
5. 记录发送状态
   ↓
6. 更新用户发送记录
```

## 5. 安全设计

### 5.1 认证安全
- JWT令牌有效期设置
- 刷新令牌机制
- 多设备登录管理
- 登录失败次数限制

### 5.2 数据安全
- 敏感信息加密存储
- 数据传输HTTPS加密
- SQL注入防护
- XSS攻击防护

### 5.3 应用安全
- CORS策略配置
- CSRF防护
- 请求频率限制
- 输入数据验证

## 6. 性能优化

### 6.1 后端优化
- 数据库索引优化
- 查询缓存
- 连接池配置
- 异步处理

### 6.2 前端优化
- 代码分割
- 懒加载
- 图片优化
- HTTP缓存

### 6.3 网络优化
- CDN加速
- 资源压缩
- 请求合并
- 缓存策略

## 7. 扩展性设计

### 7.1 微服务扩展
- 模块独立部署
- 服务间通信
- 负载均衡
- 自动扩缩容

### 7.2 功能扩展
- 插件化架构
- 配置化管理
- 事件驱动
- 扩展点设计