# 部署指南

## 目录结构
```
.
├── backend/                 # 后端应用
├── frontend/                # 前端应用
├── database/                # 数据库脚本
├── nginx/                   # Nginx配置
├── scripts/                 # 部署脚本
├── docker-compose.yml       # 开发环境配置
├── docker-compose.prod.yml  # 生产环境配置
├── .env.prod                # 生产环境变量模板
└── README.md                # 项目说明
```

## 部署步骤

### 1. 环境准备
确保系统已安装以下软件：
- Docker
- Docker Compose

### 2. 配置环境变量
```bash
# 复制环境变量模板
cp .env.prod .env

# 编辑.env文件，填入实际值
vim .env
```

### 3. 启动服务
```bash
# 启动生产环境服务
./scripts/deploy.sh
```

### 4. 访问应用
- 前端: http://localhost
- 后端API: http://localhost:3000
- 数据库: localhost:3306
- Redis: localhost:6379

## 脚本说明

### deploy.sh
部署并启动所有服务：
```bash
./scripts/deploy.sh
```

### stop.sh
停止所有服务：
```bash
./scripts/stop.sh
```

### logs.sh
查看服务日志：
```bash
# 查看所有服务日志
./scripts/logs.sh all

# 查看特定服务日志
./scripts/logs.sh backend
./scripts/logs.sh frontend
./scripts/logs.sh database
./scripts/logs.sh cache
```

## 配置说明

### 数据库配置
- 使用MySQL 8.0
- 默认端口: 3306
- 数据持久化存储在Docker卷中

### Redis配置
- 使用Redis 7-alpine
- 默认端口: 6379
- 数据持久化存储在Docker卷中

### Nginx配置
- 前端静态文件服务
- API请求反向代理到后端
- 支持Gzip压缩

## 故障排除

### 服务无法启动
1. 检查Docker是否正在运行
2. 检查端口是否被占用
3. 查看服务日志以获取详细错误信息

### 数据库连接失败
1. 检查数据库配置是否正确
2. 确认数据库服务是否正常运行
3. 检查防火墙设置

### 邮件服务不工作
1. 检查邮件配置是否正确
2. 确认SMTP服务器设置
3. 对于Gmail，确保使用应用密码而不是账户密码