# 传统节日问候助手项目职位要求与任务分配清单

## 后端开发工程师

### 核心职责
1. **用户认证系统开发**
   - 实现用户注册、登录功能
   - 集成JWT令牌认证机制
   - 实现密码加密存储
   - 开发用户会话管理

2. **节日管理模块开发**
   - 实现节日数据的增删改查API
   - 开发节日分类和搜索功能
   - 实现节日提醒时间设置

3. **好友管理模块开发**
   - 实现好友信息管理API
   - 开发好友分组功能
   - 实现好友搜索和导入导出

4. **问候模板模块开发**
   - 实现问候模板管理API
   - 开发模板分类和收藏功能
   - 实现模板预览和编辑

5. **邮件提醒服务开发**
   - 实现定时任务调度
   - 集成邮件发送服务
   - 开发邮件记录管理
   - 实现发送失败重试机制

### 技术要求
- 精通Java编程语言和Spring Boot框架
- 熟悉MySQL数据库设计和JPA/Hibernate
- 掌握Redis缓存使用和优化
- 熟悉JWT安全认证和Spring Security
- 了解邮件发送服务集成(Spring Mail)
- 掌握JUnit/Mockito单元测试框架

### 交付物
- 完整的后端API服务
- 数据库表结构和索引优化
- API接口文档
- 单元测试和集成测试
- 部署配置文件

## 前端开发工程师

### 核心职责
1. **用户界面开发**
   - 实现登录注册页面
   - 开发个人中心和设置页面
   - 实现响应式布局设计

2. **节日展示页面开发**
   - 开发节日列表和详情页面
   - 实现节日搜索和筛选功能
   - 开发节日提醒设置界面

3. **好友管理界面开发**
   - 实现好友列表和详情页面
   - 开发好友添加和编辑功能
   - 实现好友分组管理界面

4. **问候模板界面开发**
   - 开发模板展示和选择页面
   - 实现模板创建和编辑功能
   - 开发模板分类和收藏功能

5. **邮件记录界面开发**
   - 实现邮件发送记录展示
   - 开发邮件状态跟踪功能
   - 实现邮件重发功能

### 技术要求
- 精通Vue 3和TypeScript
- 熟悉Vite构建工具和组件化开发
- 掌握Tailwind CSS样式框架
- 熟悉Axios HTTP客户端和服务集成
- 了解Pinia状态管理和Vue Router
- 掌握Vitest/Cypress测试工具

### 交付物
- 完整的前端用户界面
- 响应式UI组件库
- 与后端API的完整集成
- 前端单元测试和端到端测试
- 用户体验优化和性能调优

## 测试工程师

### 核心职责
1. **测试计划制定**
   - 制定详细的测试计划和策略
   - 设计测试用例和测试数据
   - 确定测试环境和工具配置

2. **功能测试执行**
   - 执行手动功能测试
   - 进行回归测试和边界测试
   - 编写测试报告和缺陷记录

3. **自动化测试实施**
   - 实施后端单元测试(JUnit/Mockito)
   - 实施前端单元测试(Vitest)
   - 实施端到端测试(Cypress)
   - 实施API接口测试(Postman)

4. **性能和安全测试**
   - 进行系统性能测试(JMeter)
   - 执行安全漏洞扫描
   - 进行压力测试和负载测试

5. **测试环境管理**
   - 搭建和维护测试环境
   - 管理测试数据和配置
   - 实施测试自动化流水线

### 技术要求
- 掌握JUnit 5和Mockito测试框架
- 熟悉Spring Boot Test集成测试
- 了解Vitest前端测试框架
- 掌握Cypress端到端测试工具
- 熟悉Postman API测试工具
- 了解JMeter性能测试工具

### 交付物
- 完整的测试计划和用例
- 测试执行报告和缺陷分析
- 自动化测试套件
- 性能测试报告
- 安全测试评估

## DevOps工程师

### 核心职责
1. **容器化部署**
   - 设计Docker容器化部署方案
   - 编写Dockerfile和Docker Compose配置
   - 实施多容器编排和网络配置

2. **持续集成/持续部署**
   - 建立CI/CD流水线(GitHub Actions)
   - 实施自动化构建和部署
   - 配置环境变量和密钥管理

3. **系统监控和告警**
   - 配置Prometheus监控系统
   - 建立Grafana仪表板
   - 实施日志收集和分析
   - 配置告警机制和通知

4. **负载均衡和反向代理**
   - 配置Nginx反向代理
   - 实施负载均衡策略
   - 配置SSL证书和HTTPS

5. **生产环境管理**
   - 管理生产服务器和资源配置
   - 实施备份和恢复策略
   - 进行系统性能优化
   - 实施安全加固措施

### 技术要求
- 精通Docker和Docker Compose
- 熟悉Nginx配置和优化
- 掌握GitHub Actions CI/CD
- 了解Prometheus和Grafana监控
- 熟悉Linux系统管理和Shell脚本
- 掌握云服务部署和管理

### 交付物
- 完整的容器化部署方案
- CI/CD流水线配置
- 监控告警系统
- 生产环境部署架构
- 运维文档和操作手册

---

这份职位要求清单可以直接用于任务分配，明确了每个职位的核心职责、技术要求和交付物，便于您根据项目需求分配给不同的Agent执行。