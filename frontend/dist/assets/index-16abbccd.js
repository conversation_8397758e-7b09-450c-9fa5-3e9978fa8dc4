(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.18
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function or(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ee={},jt=[],Ue=()=>{},Ll=()=>!1,es=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ir=e=>e.startsWith("onUpdate:"),ae=Object.assign,lr=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Ml=Object.prototype.hasOwnProperty,Q=(e,t)=>Ml.call(e,t),B=Array.isArray,Bt=e=>ts(e)==="[object Map]",Ko=e=>ts(e)==="[object Set]",H=e=>typeof e=="function",ce=e=>typeof e=="string",wt=e=>typeof e=="symbol",ie=e=>e!==null&&typeof e=="object",Wo=e=>(ie(e)||H(e))&&H(e.then)&&H(e.catch),zo=Object.prototype.toString,ts=e=>zo.call(e),Dl=e=>ts(e).slice(8,-1),Jo=e=>ts(e)==="[object Object]",cr=e=>ce(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,sn=or(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),ns=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ul=/-(\w)/g,Fe=ns(e=>e.replace(Ul,(t,n)=>n?n.toUpperCase():"")),$l=/\B([A-Z])/g,It=ns(e=>e.replace($l,"-$1").toLowerCase()),ss=ns(e=>e.charAt(0).toUpperCase()+e.slice(1)),ws=ns(e=>e?`on${ss(e)}`:""),bt=(e,t)=>!Object.is(e,t),Ln=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Us=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},$s=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Dr;const rs=()=>Dr||(Dr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function ur(e){if(B(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=ce(s)?Hl(s):ur(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(ce(e)||ie(e))return e}const kl=/;(?![^(]*\))/g,jl=/:([^]+)/,Bl=/\/\*[^]*?\*\//g;function Hl(e){const t={};return e.replace(Bl,"").split(kl).forEach(n=>{if(n){const s=n.split(jl);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Ot(e){let t="";if(ce(e))t=e;else if(B(e))for(let n=0;n<e.length;n++){const s=Ot(e[n]);s&&(t+=s+" ")}else if(ie(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const ql="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Vl=or(ql);function Go(e){return!!e||e===""}const Xo=e=>!!(e&&e.__v_isRef===!0),yt=e=>ce(e)?e:e==null?"":B(e)||ie(e)&&(e.toString===zo||!H(e.toString))?Xo(e)?yt(e.value):JSON.stringify(e,Qo,2):String(e),Qo=(e,t)=>Xo(t)?Qo(e,t.value):Bt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[vs(s,o)+" =>"]=r,n),{})}:Ko(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>vs(n))}:wt(t)?vs(t):ie(t)&&!B(t)&&!Jo(t)?String(t):t,vs=(e,t="")=>{var n;return wt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let xe;class Kl{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=xe,!t&&xe&&(this.index=(xe.scopes||(xe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=xe;try{return xe=this,t()}finally{xe=n}}}on(){++this._on===1&&(this.prevScope=xe,xe=this)}off(){this._on>0&&--this._on===0&&(xe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Wl(){return xe}let se;const xs=new WeakSet;class Yo{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,xe&&xe.active&&xe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,xs.has(this)&&(xs.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ei(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Ur(this),ti(this);const t=se,n=$e;se=this,$e=!0;try{return this.fn()}finally{ni(this),se=t,$e=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)dr(t);this.deps=this.depsTail=void 0,Ur(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?xs.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){ks(this)&&this.run()}get dirty(){return ks(this)}}let Zo=0,rn,on;function ei(e,t=!1){if(e.flags|=8,t){e.next=on,on=e;return}e.next=rn,rn=e}function ar(){Zo++}function fr(){if(--Zo>0)return;if(on){let t=on;for(on=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;rn;){let t=rn;for(rn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function ti(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function ni(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),dr(s),zl(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function ks(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(si(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function si(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===gn)||(e.globalVersion=gn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!ks(e))))return;e.flags|=2;const t=e.dep,n=se,s=$e;se=e,$e=!0;try{ti(e);const r=e.fn(e._value);(t.version===0||bt(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{se=n,$e=s,ni(e),e.flags&=-3}}function dr(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)dr(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function zl(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let $e=!0;const ri=[];function ct(){ri.push($e),$e=!1}function ut(){const e=ri.pop();$e=e===void 0?!0:e}function Ur(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=se;se=void 0;try{t()}finally{se=n}}}let gn=0;class Jl{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class pr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!se||!$e||se===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==se)n=this.activeLink=new Jl(se,this),se.deps?(n.prevDep=se.depsTail,se.depsTail.nextDep=n,se.depsTail=n):se.deps=se.depsTail=n,oi(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=se.depsTail,n.nextDep=void 0,se.depsTail.nextDep=n,se.depsTail=n,se.deps===n&&(se.deps=s)}return n}trigger(t){this.version++,gn++,this.notify(t)}notify(t){ar();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{fr()}}}function oi(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)oi(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const js=new WeakMap,Tt=Symbol(""),Bs=Symbol(""),bn=Symbol("");function de(e,t,n){if($e&&se){let s=js.get(e);s||js.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new pr),r.map=s,r.key=n),r.track()}}function ot(e,t,n,s,r,o){const i=js.get(e);if(!i){gn++;return}const l=c=>{c&&c.trigger()};if(ar(),t==="clear")i.forEach(l);else{const c=B(e),a=c&&cr(n);if(c&&n==="length"){const u=Number(s);i.forEach((d,h)=>{(h==="length"||h===bn||!wt(h)&&h>=u)&&l(d)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),a&&l(i.get(bn)),t){case"add":c?a&&l(i.get("length")):(l(i.get(Tt)),Bt(e)&&l(i.get(Bs)));break;case"delete":c||(l(i.get(Tt)),Bt(e)&&l(i.get(Bs)));break;case"set":Bt(e)&&l(i.get(Tt));break}}fr()}function Dt(e){const t=X(e);return t===e?t:(de(t,"iterate",bn),ke(e)?t:t.map(ye))}function hr(e){return de(e=X(e),"iterate",bn),e}const Gl={__proto__:null,[Symbol.iterator](){return Es(this,Symbol.iterator,ye)},concat(...e){return Dt(this).concat(...e.map(t=>B(t)?Dt(t):t))},entries(){return Es(this,"entries",e=>(e[1]=ye(e[1]),e))},every(e,t){return tt(this,"every",e,t,void 0,arguments)},filter(e,t){return tt(this,"filter",e,t,n=>n.map(ye),arguments)},find(e,t){return tt(this,"find",e,t,ye,arguments)},findIndex(e,t){return tt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return tt(this,"findLast",e,t,ye,arguments)},findLastIndex(e,t){return tt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return tt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ss(this,"includes",e)},indexOf(...e){return Ss(this,"indexOf",e)},join(e){return Dt(this).join(e)},lastIndexOf(...e){return Ss(this,"lastIndexOf",e)},map(e,t){return tt(this,"map",e,t,void 0,arguments)},pop(){return Yt(this,"pop")},push(...e){return Yt(this,"push",e)},reduce(e,...t){return $r(this,"reduce",e,t)},reduceRight(e,...t){return $r(this,"reduceRight",e,t)},shift(){return Yt(this,"shift")},some(e,t){return tt(this,"some",e,t,void 0,arguments)},splice(...e){return Yt(this,"splice",e)},toReversed(){return Dt(this).toReversed()},toSorted(e){return Dt(this).toSorted(e)},toSpliced(...e){return Dt(this).toSpliced(...e)},unshift(...e){return Yt(this,"unshift",e)},values(){return Es(this,"values",ye)}};function Es(e,t,n){const s=hr(e),r=s[t]();return s!==e&&!ke(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const Xl=Array.prototype;function tt(e,t,n,s,r,o){const i=hr(e),l=i!==e&&!ke(e),c=i[t];if(c!==Xl[t]){const d=c.apply(e,o);return l?ye(d):d}let a=n;i!==e&&(l?a=function(d,h){return n.call(this,ye(d),h,e)}:n.length>2&&(a=function(d,h){return n.call(this,d,h,e)}));const u=c.call(i,a,s);return l&&r?r(u):u}function $r(e,t,n,s){const r=hr(e);let o=n;return r!==e&&(ke(e)?n.length>3&&(o=function(i,l,c){return n.call(this,i,l,c,e)}):o=function(i,l,c){return n.call(this,i,ye(l),c,e)}),r[t](o,...s)}function Ss(e,t,n){const s=X(e);de(s,"iterate",bn);const r=s[t](...n);return(r===-1||r===!1)&&br(n[0])?(n[0]=X(n[0]),s[t](...n)):r}function Yt(e,t,n=[]){ct(),ar();const s=X(e)[t].apply(e,n);return fr(),ut(),s}const Ql=or("__proto__,__v_isRef,__isVue"),ii=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(wt));function Yl(e){wt(e)||(e=String(e));const t=X(this);return de(t,"has",e),t.hasOwnProperty(e)}class li{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?cc:fi:o?ai:ui).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=B(t);if(!r){let c;if(i&&(c=Gl[n]))return c;if(n==="hasOwnProperty")return Yl}const l=Reflect.get(t,n,me(t)?t:s);return(wt(n)?ii.has(n):Ql(n))||(r||de(t,"get",n),o)?l:me(l)?i&&cr(n)?l:l.value:ie(l)?r?pi(l):os(l):l}}class ci extends li{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const c=Pt(o);if(!ke(s)&&!Pt(s)&&(o=X(o),s=X(s)),!B(t)&&me(o)&&!me(s))return c?!1:(o.value=s,!0)}const i=B(t)&&cr(n)?Number(n)<t.length:Q(t,n),l=Reflect.set(t,n,s,me(t)?t:r);return t===X(r)&&(i?bt(s,o)&&ot(t,"set",n,s):ot(t,"add",n,s)),l}deleteProperty(t,n){const s=Q(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&ot(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!wt(n)||!ii.has(n))&&de(t,"has",n),s}ownKeys(t){return de(t,"iterate",B(t)?"length":Tt),Reflect.ownKeys(t)}}class Zl extends li{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const ec=new ci,tc=new Zl,nc=new ci(!0);const Hs=e=>e,Nn=e=>Reflect.getPrototypeOf(e);function sc(e,t,n){return function(...s){const r=this.__v_raw,o=X(r),i=Bt(o),l=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,a=r[e](...s),u=n?Hs:t?qs:ye;return!t&&de(o,"iterate",c?Bs:Tt),{next(){const{value:d,done:h}=a.next();return h?{value:d,done:h}:{value:l?[u(d[0]),u(d[1])]:u(d),done:h}},[Symbol.iterator](){return this}}}}function In(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function rc(e,t){const n={get(r){const o=this.__v_raw,i=X(o),l=X(r);e||(bt(r,l)&&de(i,"get",r),de(i,"get",l));const{has:c}=Nn(i),a=t?Hs:e?qs:ye;if(c.call(i,r))return a(o.get(r));if(c.call(i,l))return a(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&de(X(r),"iterate",Tt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=X(o),l=X(r);return e||(bt(r,l)&&de(i,"has",r),de(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,c=X(l),a=t?Hs:e?qs:ye;return!e&&de(c,"iterate",Tt),l.forEach((u,d)=>r.call(o,a(u),a(d),i))}};return ae(n,e?{add:In("add"),set:In("set"),delete:In("delete"),clear:In("clear")}:{add(r){!t&&!ke(r)&&!Pt(r)&&(r=X(r));const o=X(this);return Nn(o).has.call(o,r)||(o.add(r),ot(o,"add",r,r)),this},set(r,o){!t&&!ke(o)&&!Pt(o)&&(o=X(o));const i=X(this),{has:l,get:c}=Nn(i);let a=l.call(i,r);a||(r=X(r),a=l.call(i,r));const u=c.call(i,r);return i.set(r,o),a?bt(o,u)&&ot(i,"set",r,o):ot(i,"add",r,o),this},delete(r){const o=X(this),{has:i,get:l}=Nn(o);let c=i.call(o,r);c||(r=X(r),c=i.call(o,r)),l&&l.call(o,r);const a=o.delete(r);return c&&ot(o,"delete",r,void 0),a},clear(){const r=X(this),o=r.size!==0,i=r.clear();return o&&ot(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=sc(r,e,t)}),n}function mr(e,t){const n=rc(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(Q(n,r)&&r in s?n:s,r,o)}const oc={get:mr(!1,!1)},ic={get:mr(!1,!0)},lc={get:mr(!0,!1)};const ui=new WeakMap,ai=new WeakMap,fi=new WeakMap,cc=new WeakMap;function uc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function ac(e){return e.__v_skip||!Object.isExtensible(e)?0:uc(Dl(e))}function os(e){return Pt(e)?e:gr(e,!1,ec,oc,ui)}function di(e){return gr(e,!1,nc,ic,ai)}function pi(e){return gr(e,!0,tc,lc,fi)}function gr(e,t,n,s,r){if(!ie(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=ac(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function ln(e){return Pt(e)?ln(e.__v_raw):!!(e&&e.__v_isReactive)}function Pt(e){return!!(e&&e.__v_isReadonly)}function ke(e){return!!(e&&e.__v_isShallow)}function br(e){return e?!!e.__v_raw:!1}function X(e){const t=e&&e.__v_raw;return t?X(t):e}function fc(e){return!Q(e,"__v_skip")&&Object.isExtensible(e)&&Us(e,"__v_skip",!0),e}const ye=e=>ie(e)?os(e):e,qs=e=>ie(e)?pi(e):e;function me(e){return e?e.__v_isRef===!0:!1}function lt(e){return hi(e,!1)}function dc(e){return hi(e,!0)}function hi(e,t){return me(e)?e:new pc(e,t)}class pc{constructor(t,n){this.dep=new pr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:X(t),this._value=n?t:ye(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||ke(t)||Pt(t);t=s?t:X(t),bt(t,n)&&(this._rawValue=t,this._value=s?t:ye(t),this.dep.trigger())}}function Ct(e){return me(e)?e.value:e}const hc={get:(e,t,n)=>t==="__v_raw"?e:Ct(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return me(r)&&!me(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function mi(e){return ln(e)?e:new Proxy(e,hc)}class mc{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new pr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=gn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&se!==this)return ei(this,!0),!0}get value(){const t=this.dep.track();return si(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function gc(e,t,n=!1){let s,r;return H(e)?s=e:(s=e.get,r=e.set),new mc(s,r,n)}const Fn={},Vn=new WeakMap;let Rt;function bc(e,t=!1,n=Rt){if(n){let s=Vn.get(n);s||Vn.set(n,s=[]),s.push(e)}}function yc(e,t,n=ee){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:l,call:c}=n,a=L=>r?L:ke(L)||r===!1||r===0?it(L,1):it(L);let u,d,h,m,y=!1,v=!1;if(me(e)?(d=()=>e.value,y=ke(e)):ln(e)?(d=()=>a(e),y=!0):B(e)?(v=!0,y=e.some(L=>ln(L)||ke(L)),d=()=>e.map(L=>{if(me(L))return L.value;if(ln(L))return a(L);if(H(L))return c?c(L,2):L()})):H(e)?t?d=c?()=>c(e,2):e:d=()=>{if(h){ct();try{h()}finally{ut()}}const L=Rt;Rt=u;try{return c?c(e,3,[m]):e(m)}finally{Rt=L}}:d=Ue,t&&r){const L=d,q=r===!0?1/0:r;d=()=>it(L(),q)}const S=Wl(),P=()=>{u.stop(),S&&S.active&&lr(S.effects,u)};if(o&&t){const L=t;t=(...q)=>{L(...q),P()}}let T=v?new Array(e.length).fill(Fn):Fn;const F=L=>{if(!(!(u.flags&1)||!u.dirty&&!L))if(t){const q=u.run();if(r||y||(v?q.some((te,W)=>bt(te,T[W])):bt(q,T))){h&&h();const te=Rt;Rt=u;try{const W=[q,T===Fn?void 0:v&&T[0]===Fn?[]:T,m];T=q,c?c(t,3,W):t(...W)}finally{Rt=te}}}else u.run()};return l&&l(F),u=new Yo(d),u.scheduler=i?()=>i(F,!1):F,m=L=>bc(L,!1,u),h=u.onStop=()=>{const L=Vn.get(u);if(L){if(c)c(L,4);else for(const q of L)q();Vn.delete(u)}},t?s?F(!0):T=u.run():i?i(F.bind(null,!0),!0):u.run(),P.pause=u.pause.bind(u),P.resume=u.resume.bind(u),P.stop=P,P}function it(e,t=1/0,n){if(t<=0||!ie(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,me(e))it(e.value,t,n);else if(B(e))for(let s=0;s<e.length;s++)it(e[s],t,n);else if(Ko(e)||Bt(e))e.forEach(s=>{it(s,t,n)});else if(Jo(e)){for(const s in e)it(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&it(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Sn(e,t,n,s){try{return s?e(...s):e()}catch(r){is(r,t,n)}}function Ze(e,t,n,s){if(H(e)){const r=Sn(e,t,n,s);return r&&Wo(r)&&r.catch(o=>{is(o,t,n)}),r}if(B(e)){const r=[];for(let o=0;o<e.length;o++)r.push(Ze(e[o],t,n,s));return r}}function is(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||ee;if(t){let l=t.parent;const c=t.proxy,a=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const u=l.ec;if(u){for(let d=0;d<u.length;d++)if(u[d](e,c,a)===!1)return}l=l.parent}if(o){ct(),Sn(o,null,10,[e,c,a]),ut();return}}_c(e,n,r,s,i)}function _c(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const _e=[];let Xe=-1;const Ht=[];let ht=null,Ut=0;const gi=Promise.resolve();let Kn=null;function bi(e){const t=Kn||gi;return e?t.then(this?e.bind(this):e):t}function wc(e){let t=Xe+1,n=_e.length;for(;t<n;){const s=t+n>>>1,r=_e[s],o=yn(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function yr(e){if(!(e.flags&1)){const t=yn(e),n=_e[_e.length-1];!n||!(e.flags&2)&&t>=yn(n)?_e.push(e):_e.splice(wc(t),0,e),e.flags|=1,yi()}}function yi(){Kn||(Kn=gi.then(wi))}function vc(e){B(e)?Ht.push(...e):ht&&e.id===-1?ht.splice(Ut+1,0,e):e.flags&1||(Ht.push(e),e.flags|=1),yi()}function kr(e,t,n=Xe+1){for(;n<_e.length;n++){const s=_e[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;_e.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function _i(e){if(Ht.length){const t=[...new Set(Ht)].sort((n,s)=>yn(n)-yn(s));if(Ht.length=0,ht){ht.push(...t);return}for(ht=t,Ut=0;Ut<ht.length;Ut++){const n=ht[Ut];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}ht=null,Ut=0}}const yn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function wi(e){const t=Ue;try{for(Xe=0;Xe<_e.length;Xe++){const n=_e[Xe];n&&!(n.flags&8)&&(n.flags&4&&(n.flags&=-2),Sn(n,n.i,n.i?15:14),n.flags&4||(n.flags&=-2))}}finally{for(;Xe<_e.length;Xe++){const n=_e[Xe];n&&(n.flags&=-2)}Xe=-1,_e.length=0,_i(),Kn=null,(_e.length||Ht.length)&&wi()}}let Te=null,vi=null;function Wn(e){const t=Te;return Te=e,vi=e&&e.type.__scopeId||null,t}function Ye(e,t=Te,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Jr(-1);const o=Wn(t);let i;try{i=e(...r)}finally{Wn(o),s._d&&Jr(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function cn(e,t){if(Te===null)return e;const n=as(Te),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,c=ee]=t[r];o&&(H(o)&&(o={mounted:o,updated:o}),o.deep&&it(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:c}))}return e}function Et(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let c=l.dir[s];c&&(ct(),Ze(c,n,8,[e.el,l,e,t]),ut())}}const xc=Symbol("_vte"),Ec=e=>e.__isTeleport;function _r(e,t){e.shapeFlag&6&&e.component?(e.transition=t,_r(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function vt(e,t){return H(e)?(()=>ae({name:e.name},t,{setup:e}))():e}function xi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function un(e,t,n,s,r=!1){if(B(e)){e.forEach((y,v)=>un(y,t&&(B(t)?t[v]:t),n,s,r));return}if(an(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&un(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?as(s.component):s.el,i=r?null:o,{i:l,r:c}=e,a=t&&t.r,u=l.refs===ee?l.refs={}:l.refs,d=l.setupState,h=X(d),m=d===ee?()=>!1:y=>Q(h,y);if(a!=null&&a!==c&&(ce(a)?(u[a]=null,m(a)&&(d[a]=null)):me(a)&&(a.value=null)),H(c))Sn(c,l,12,[i,u]);else{const y=ce(c),v=me(c);if(y||v){const S=()=>{if(e.f){const P=y?m(c)?d[c]:u[c]:c.value;r?B(P)&&lr(P,o):B(P)?P.includes(o)||P.push(o):y?(u[c]=[o],m(c)&&(d[c]=u[c])):(c.value=[o],e.k&&(u[e.k]=c.value))}else y?(u[c]=i,m(c)&&(d[c]=i)):v&&(c.value=i,e.k&&(u[e.k]=i))};i?(S.id=-1,Oe(S,n)):S()}}}rs().requestIdleCallback;rs().cancelIdleCallback;const an=e=>!!e.type.__asyncLoader,Ei=e=>e.type.__isKeepAlive;function Sc(e,t){Si(e,"a",t)}function Rc(e,t){Si(e,"da",t)}function Si(e,t,n=pe){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(ls(t,s,n),n){let r=n.parent;for(;r&&r.parent;)Ei(r.parent.vnode)&&Oc(s,t,n,r),r=r.parent}}function Oc(e,t,n,s){const r=ls(t,e,s,!0);Oi(()=>{lr(s[t],r)},n)}function ls(e,t,n=pe,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{ct();const l=On(n),c=Ze(t,n,e,i);return l(),ut(),c});return s?r.unshift(o):r.push(o),o}}const at=e=>(t,n=pe)=>{(!wn||e==="sp")&&ls(e,(...s)=>t(...s),n)},Ac=at("bm"),Ri=at("m"),Tc=at("bu"),Cc=at("u"),Pc=at("bum"),Oi=at("um"),Nc=at("sp"),Ic=at("rtg"),Fc=at("rtc");function Lc(e,t=pe){ls("ec",e,t)}const Ai="components";function Rn(e,t){return Dc(Ai,e,!0,t)||e}const Mc=Symbol.for("v-ndc");function Dc(e,t,n=!0,s=!1){const r=Te||pe;if(r){const o=r.type;if(e===Ai){const l=Ru(o,!1);if(l&&(l===t||l===Fe(t)||l===ss(Fe(t))))return o}const i=jr(r[e]||o[e],t)||jr(r.appContext[e],t);return!i&&s?o:i}}function jr(e,t){return e&&(e[t]||e[Fe(t)]||e[ss(Fe(t))])}const Vs=e=>e?zi(e)?as(e):Vs(e.parent):null,fn=ae(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Vs(e.parent),$root:e=>Vs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>wr(e),$forceUpdate:e=>e.f||(e.f=()=>{yr(e.update)}),$nextTick:e=>e.n||(e.n=bi.bind(e.proxy)),$watch:e=>su.bind(e)}),Rs=(e,t)=>e!==ee&&!e.__isScriptSetup&&Q(e,t),Uc={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:l,appContext:c}=e;let a;if(t[0]!=="$"){const m=i[t];if(m!==void 0)switch(m){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(Rs(s,t))return i[t]=1,s[t];if(r!==ee&&Q(r,t))return i[t]=2,r[t];if((a=e.propsOptions[0])&&Q(a,t))return i[t]=3,o[t];if(n!==ee&&Q(n,t))return i[t]=4,n[t];Ks&&(i[t]=0)}}const u=fn[t];let d,h;if(u)return t==="$attrs"&&de(e.attrs,"get",""),u(e);if((d=l.__cssModules)&&(d=d[t]))return d;if(n!==ee&&Q(n,t))return i[t]=4,n[t];if(h=c.config.globalProperties,Q(h,t))return h[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return Rs(r,t)?(r[t]=n,!0):s!==ee&&Q(s,t)?(s[t]=n,!0):Q(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let l;return!!n[i]||e!==ee&&Q(e,i)||Rs(t,i)||(l=o[0])&&Q(l,i)||Q(s,i)||Q(fn,i)||Q(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:Q(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Br(e){return B(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Ks=!0;function $c(e){const t=wr(e),n=e.proxy,s=e.ctx;Ks=!1,t.beforeCreate&&Hr(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:c,inject:a,created:u,beforeMount:d,mounted:h,beforeUpdate:m,updated:y,activated:v,deactivated:S,beforeDestroy:P,beforeUnmount:T,destroyed:F,unmounted:L,render:q,renderTracked:te,renderTriggered:W,errorCaptured:ge,serverPrefetch:Le,expose:Ve,inheritAttrs:ft,components:xt,directives:Ke,filters:Xt}=t;if(a&&kc(a,s,null),i)for(const Z in i){const z=i[Z];H(z)&&(s[Z]=z.bind(n))}if(r){const Z=r.call(n,n);ie(Z)&&(e.data=os(Z))}if(Ks=!0,o)for(const Z in o){const z=o[Z],et=H(z)?z.bind(n,n):H(z.get)?z.get.bind(n,n):Ue,dt=!H(z)&&H(z.set)?z.set.bind(n):Ue,We=De({get:et,set:dt});Object.defineProperty(s,Z,{enumerable:!0,configurable:!0,get:()=>We.value,set:we=>We.value=we})}if(l)for(const Z in l)Ti(l[Z],s,n,Z);if(c){const Z=H(c)?c.call(n):c;Reflect.ownKeys(Z).forEach(z=>{Mn(z,Z[z])})}u&&Hr(u,e,"c");function ue(Z,z){B(z)?z.forEach(et=>Z(et.bind(n))):z&&Z(z.bind(n))}if(ue(Ac,d),ue(Ri,h),ue(Tc,m),ue(Cc,y),ue(Sc,v),ue(Rc,S),ue(Lc,ge),ue(Fc,te),ue(Ic,W),ue(Pc,T),ue(Oi,L),ue(Nc,Le),B(Ve))if(Ve.length){const Z=e.exposed||(e.exposed={});Ve.forEach(z=>{Object.defineProperty(Z,z,{get:()=>n[z],set:et=>n[z]=et,enumerable:!0})})}else e.exposed||(e.exposed={});q&&e.render===Ue&&(e.render=q),ft!=null&&(e.inheritAttrs=ft),xt&&(e.components=xt),Ke&&(e.directives=Ke),Le&&xi(e)}function kc(e,t,n=Ue){B(e)&&(e=Ws(e));for(const s in e){const r=e[s];let o;ie(r)?"default"in r?o=je(r.from||s,r.default,!0):o=je(r.from||s):o=je(r),me(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function Hr(e,t,n){Ze(B(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Ti(e,t,n,s){let r=s.includes(".")?Bi(n,s):()=>n[s];if(ce(e)){const o=t[e];H(o)&&Dn(r,o)}else if(H(e))Dn(r,e.bind(n));else if(ie(e))if(B(e))e.forEach(o=>Ti(o,t,n,s));else{const o=H(e.handler)?e.handler.bind(n):t[e.handler];H(o)&&Dn(r,o,e)}}function wr(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(a=>zn(c,a,i,!0)),zn(c,t,i)),ie(t)&&o.set(t,c),c}function zn(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&zn(e,o,n,!0),r&&r.forEach(i=>zn(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=jc[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const jc={data:qr,props:Vr,emits:Vr,methods:nn,computed:nn,beforeCreate:be,created:be,beforeMount:be,mounted:be,beforeUpdate:be,updated:be,beforeDestroy:be,beforeUnmount:be,destroyed:be,unmounted:be,activated:be,deactivated:be,errorCaptured:be,serverPrefetch:be,components:nn,directives:nn,watch:Hc,provide:qr,inject:Bc};function qr(e,t){return t?e?function(){return ae(H(e)?e.call(this,this):e,H(t)?t.call(this,this):t)}:t:e}function Bc(e,t){return nn(Ws(e),Ws(t))}function Ws(e){if(B(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function be(e,t){return e?[...new Set([].concat(e,t))]:t}function nn(e,t){return e?ae(Object.create(null),e,t):t}function Vr(e,t){return e?B(e)&&B(t)?[...new Set([...e,...t])]:ae(Object.create(null),Br(e),Br(t??{})):t}function Hc(e,t){if(!e)return t;if(!t)return e;const n=ae(Object.create(null),e);for(const s in t)n[s]=be(e[s],t[s]);return n}function Ci(){return{app:null,config:{isNativeTag:Ll,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let qc=0;function Vc(e,t){return function(s,r=null){H(s)||(s=ae({},s)),r!=null&&!ie(r)&&(r=null);const o=Ci(),i=new WeakSet,l=[];let c=!1;const a=o.app={_uid:qc++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:Au,get config(){return o.config},set config(u){},use(u,...d){return i.has(u)||(u&&H(u.install)?(i.add(u),u.install(a,...d)):H(u)&&(i.add(u),u(a,...d))),a},mixin(u){return o.mixins.includes(u)||o.mixins.push(u),a},component(u,d){return d?(o.components[u]=d,a):o.components[u]},directive(u,d){return d?(o.directives[u]=d,a):o.directives[u]},mount(u,d,h){if(!c){const m=a._ceVNode||re(s,r);return m.appContext=o,h===!0?h="svg":h===!1&&(h=void 0),d&&t?t(m,u):e(m,u,h),c=!0,a._container=u,u.__vue_app__=a,as(m.component)}},onUnmount(u){l.push(u)},unmount(){c&&(Ze(l,a._instance,16),e(null,a._container),delete a._container.__vue_app__)},provide(u,d){return o.provides[u]=d,a},runWithContext(u){const d=qt;qt=a;try{return u()}finally{qt=d}}};return a}}let qt=null;function Mn(e,t){if(pe){let n=pe.provides;const s=pe.parent&&pe.parent.provides;s===n&&(n=pe.provides=Object.create(s)),n[e]=t}}function je(e,t,n=!1){const s=wu();if(s||qt){let r=qt?qt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&H(t)?t.call(s&&s.proxy):t}}const Pi={},Ni=()=>Object.create(Pi),Ii=e=>Object.getPrototypeOf(e)===Pi;function Kc(e,t,n,s=!1){const r={},o=Ni();e.propsDefaults=Object.create(null),Fi(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:di(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Wc(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=X(r),[c]=e.propsOptions;let a=!1;if((s||i>0)&&!(i&16)){if(i&8){const u=e.vnode.dynamicProps;for(let d=0;d<u.length;d++){let h=u[d];if(cs(e.emitsOptions,h))continue;const m=t[h];if(c)if(Q(o,h))m!==o[h]&&(o[h]=m,a=!0);else{const y=Fe(h);r[y]=zs(c,l,y,m,e,!1)}else m!==o[h]&&(o[h]=m,a=!0)}}}else{Fi(e,t,r,o)&&(a=!0);let u;for(const d in l)(!t||!Q(t,d)&&((u=It(d))===d||!Q(t,u)))&&(c?n&&(n[d]!==void 0||n[u]!==void 0)&&(r[d]=zs(c,l,d,void 0,e,!0)):delete r[d]);if(o!==l)for(const d in o)(!t||!Q(t,d))&&(delete o[d],a=!0)}a&&ot(e.attrs,"set","")}function Fi(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let c in t){if(sn(c))continue;const a=t[c];let u;r&&Q(r,u=Fe(c))?!o||!o.includes(u)?n[u]=a:(l||(l={}))[u]=a:cs(e.emitsOptions,c)||(!(c in s)||a!==s[c])&&(s[c]=a,i=!0)}if(o){const c=X(n),a=l||ee;for(let u=0;u<o.length;u++){const d=o[u];n[d]=zs(r,c,d,a[d],e,!Q(a,d))}}return i}function zs(e,t,n,s,r,o){const i=e[n];if(i!=null){const l=Q(i,"default");if(l&&s===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&H(c)){const{propsDefaults:a}=r;if(n in a)s=a[n];else{const u=On(r);s=a[n]=c.call(null,t),u()}}else s=c;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!l?s=!1:i[1]&&(s===""||s===It(n))&&(s=!0))}return s}const zc=new WeakMap;function Li(e,t,n=!1){const s=n?zc:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},l=[];let c=!1;if(!H(e)){const u=d=>{c=!0;const[h,m]=Li(d,t,!0);ae(i,h),m&&l.push(...m)};!n&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!o&&!c)return ie(e)&&s.set(e,jt),jt;if(B(o))for(let u=0;u<o.length;u++){const d=Fe(o[u]);Kr(d)&&(i[d]=ee)}else if(o)for(const u in o){const d=Fe(u);if(Kr(d)){const h=o[u],m=i[d]=B(h)||H(h)?{type:h}:ae({},h),y=m.type;let v=!1,S=!0;if(B(y))for(let P=0;P<y.length;++P){const T=y[P],F=H(T)&&T.name;if(F==="Boolean"){v=!0;break}else F==="String"&&(S=!1)}else v=H(y)&&y.name==="Boolean";m[0]=v,m[1]=S,(v||Q(m,"default"))&&l.push(d)}}const a=[i,l];return ie(e)&&s.set(e,a),a}function Kr(e){return e[0]!=="$"&&!sn(e)}const vr=e=>e==="_"||e==="__"||e==="_ctx"||e==="$stable",xr=e=>B(e)?e.map(Qe):[Qe(e)],Jc=(e,t,n)=>{if(t._n)return t;const s=Ye((...r)=>xr(t(...r)),n);return s._c=!1,s},Mi=(e,t,n)=>{const s=e._ctx;for(const r in e){if(vr(r))continue;const o=e[r];if(H(o))t[r]=Jc(r,o,s);else if(o!=null){const i=xr(o);t[r]=()=>i}}},Di=(e,t)=>{const n=xr(t);e.slots.default=()=>n},Ui=(e,t,n)=>{for(const s in t)(n||!vr(s))&&(e[s]=t[s])},Gc=(e,t,n)=>{const s=e.slots=Ni();if(e.vnode.shapeFlag&32){const r=t.__;r&&Us(s,"__",r,!0);const o=t._;o?(Ui(s,t,n),n&&Us(s,"_",o,!0)):Mi(t,s)}else t&&Di(e,t)},Xc=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=ee;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:Ui(r,t,n):(o=!t.$stable,Mi(t,r)),i=t}else t&&(Di(e,t),i={default:1});if(o)for(const l in r)!vr(l)&&i[l]==null&&delete r[l]},Oe=au;function Qc(e){return Yc(e)}function Yc(e,t){const n=rs();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:l,createComment:c,setText:a,setElementText:u,parentNode:d,nextSibling:h,setScopeId:m=Ue,insertStaticContent:y}=e,v=(f,p,g,x=null,_=null,E=null,C=void 0,A=null,O=!!p.dynamicChildren)=>{if(f===p)return;f&&!Zt(f,p)&&(x=w(f),we(f,_,E,!0),f=null),p.patchFlag===-2&&(O=!1,p.dynamicChildren=null);const{type:R,ref:k,shapeFlag:I}=p;switch(R){case us:S(f,p,g,x);break;case _t:P(f,p,g,x);break;case Un:f==null&&T(p,g,x,C);break;case rt:xt(f,p,g,x,_,E,C,A,O);break;default:I&1?q(f,p,g,x,_,E,C,A,O):I&6?Ke(f,p,g,x,_,E,C,A,O):(I&64||I&128)&&R.process(f,p,g,x,_,E,C,A,O,D)}k!=null&&_?un(k,f&&f.ref,E,p||f,!p):k==null&&f&&f.ref!=null&&un(f.ref,null,E,f,!0)},S=(f,p,g,x)=>{if(f==null)s(p.el=l(p.children),g,x);else{const _=p.el=f.el;p.children!==f.children&&a(_,p.children)}},P=(f,p,g,x)=>{f==null?s(p.el=c(p.children||""),g,x):p.el=f.el},T=(f,p,g,x)=>{[f.el,f.anchor]=y(f.children,p,g,x,f.el,f.anchor)},F=({el:f,anchor:p},g,x)=>{let _;for(;f&&f!==p;)_=h(f),s(f,g,x),f=_;s(p,g,x)},L=({el:f,anchor:p})=>{let g;for(;f&&f!==p;)g=h(f),r(f),f=g;r(p)},q=(f,p,g,x,_,E,C,A,O)=>{p.type==="svg"?C="svg":p.type==="math"&&(C="mathml"),f==null?te(p,g,x,_,E,C,A,O):Le(f,p,_,E,C,A,O)},te=(f,p,g,x,_,E,C,A)=>{let O,R;const{props:k,shapeFlag:I,transition:$,dirs:j}=f;if(O=f.el=i(f.type,E,k&&k.is,k),I&8?u(O,f.children):I&16&&ge(f.children,O,null,x,_,Os(f,E),C,A),j&&Et(f,null,x,"created"),W(O,f,f.scopeId,C,x),k){for(const ne in k)ne!=="value"&&!sn(ne)&&o(O,ne,null,k[ne],E,x);"value"in k&&o(O,"value",null,k.value,E),(R=k.onVnodeBeforeMount)&&Je(R,x,f)}j&&Et(f,null,x,"beforeMount");const K=Zc(_,$);K&&$.beforeEnter(O),s(O,p,g),((R=k&&k.onVnodeMounted)||K||j)&&Oe(()=>{R&&Je(R,x,f),K&&$.enter(O),j&&Et(f,null,x,"mounted")},_)},W=(f,p,g,x,_)=>{if(g&&m(f,g),x)for(let E=0;E<x.length;E++)m(f,x[E]);if(_){let E=_.subTree;if(p===E||qi(E.type)&&(E.ssContent===p||E.ssFallback===p)){const C=_.vnode;W(f,C,C.scopeId,C.slotScopeIds,_.parent)}}},ge=(f,p,g,x,_,E,C,A,O=0)=>{for(let R=O;R<f.length;R++){const k=f[R]=A?mt(f[R]):Qe(f[R]);v(null,k,p,g,x,_,E,C,A)}},Le=(f,p,g,x,_,E,C)=>{const A=p.el=f.el;let{patchFlag:O,dynamicChildren:R,dirs:k}=p;O|=f.patchFlag&16;const I=f.props||ee,$=p.props||ee;let j;if(g&&St(g,!1),(j=$.onVnodeBeforeUpdate)&&Je(j,g,p,f),k&&Et(p,f,g,"beforeUpdate"),g&&St(g,!0),(I.innerHTML&&$.innerHTML==null||I.textContent&&$.textContent==null)&&u(A,""),R?Ve(f.dynamicChildren,R,A,g,x,Os(p,_),E):C||z(f,p,A,null,g,x,Os(p,_),E,!1),O>0){if(O&16)ft(A,I,$,g,_);else if(O&2&&I.class!==$.class&&o(A,"class",null,$.class,_),O&4&&o(A,"style",I.style,$.style,_),O&8){const K=p.dynamicProps;for(let ne=0;ne<K.length;ne++){const Y=K[ne],ve=I[Y],fe=$[Y];(fe!==ve||Y==="value")&&o(A,Y,ve,fe,_,g)}}O&1&&f.children!==p.children&&u(A,p.children)}else!C&&R==null&&ft(A,I,$,g,_);((j=$.onVnodeUpdated)||k)&&Oe(()=>{j&&Je(j,g,p,f),k&&Et(p,f,g,"updated")},x)},Ve=(f,p,g,x,_,E,C)=>{for(let A=0;A<p.length;A++){const O=f[A],R=p[A],k=O.el&&(O.type===rt||!Zt(O,R)||O.shapeFlag&198)?d(O.el):g;v(O,R,k,null,x,_,E,C,!0)}},ft=(f,p,g,x,_)=>{if(p!==g){if(p!==ee)for(const E in p)!sn(E)&&!(E in g)&&o(f,E,p[E],null,_,x);for(const E in g){if(sn(E))continue;const C=g[E],A=p[E];C!==A&&E!=="value"&&o(f,E,A,C,_,x)}"value"in g&&o(f,"value",p.value,g.value,_)}},xt=(f,p,g,x,_,E,C,A,O)=>{const R=p.el=f?f.el:l(""),k=p.anchor=f?f.anchor:l("");let{patchFlag:I,dynamicChildren:$,slotScopeIds:j}=p;j&&(A=A?A.concat(j):j),f==null?(s(R,g,x),s(k,g,x),ge(p.children||[],g,k,_,E,C,A,O)):I>0&&I&64&&$&&f.dynamicChildren?(Ve(f.dynamicChildren,$,g,_,E,C,A),(p.key!=null||_&&p===_.subTree)&&$i(f,p,!0)):z(f,p,g,k,_,E,C,A,O)},Ke=(f,p,g,x,_,E,C,A,O)=>{p.slotScopeIds=A,f==null?p.shapeFlag&512?_.ctx.activate(p,g,x,C,O):Xt(p,g,x,_,E,C,O):Ft(f,p,O)},Xt=(f,p,g,x,_,E,C)=>{const A=f.component=_u(f,x,_);if(Ei(f)&&(A.ctx.renderer=D),vu(A,!1,C),A.asyncDep){if(_&&_.registerDep(A,ue,C),!f.el){const O=A.subTree=re(_t);P(null,O,p,g),f.placeholder=O.el}}else ue(A,f,p,g,_,E,C)},Ft=(f,p,g)=>{const x=p.component=f.component;if(cu(f,p,g))if(x.asyncDep&&!x.asyncResolved){Z(x,p,g);return}else x.next=p,x.update();else p.el=f.el,x.vnode=p},ue=(f,p,g,x,_,E,C)=>{const A=()=>{if(f.isMounted){let{next:I,bu:$,u:j,parent:K,vnode:ne}=f;{const Se=ki(f);if(Se){I&&(I.el=ne.el,Z(f,I,C)),Se.asyncDep.then(()=>{f.isUnmounted||A()});return}}let Y=I,ve;St(f,!1),I?(I.el=ne.el,Z(f,I,C)):I=ne,$&&Ln($),(ve=I.props&&I.props.onVnodeBeforeUpdate)&&Je(ve,K,I,ne),St(f,!0);const fe=As(f),Me=f.subTree;f.subTree=fe,v(Me,fe,d(Me.el),w(Me),f,_,E),I.el=fe.el,Y===null&&uu(f,fe.el),j&&Oe(j,_),(ve=I.props&&I.props.onVnodeUpdated)&&Oe(()=>Je(ve,K,I,ne),_)}else{let I;const{el:$,props:j}=p,{bm:K,m:ne,parent:Y,root:ve,type:fe}=f,Me=an(p);if(St(f,!1),K&&Ln(K),!Me&&(I=j&&j.onVnodeBeforeMount)&&Je(I,Y,p),St(f,!0),$&&oe){const Se=()=>{f.subTree=As(f),oe($,f.subTree,f,_,null)};Me&&fe.__asyncHydrate?fe.__asyncHydrate($,f,Se):Se()}else{ve.ce&&ve.ce._def.shadowRoot!==!1&&ve.ce._injectChildStyle(fe);const Se=f.subTree=As(f);v(null,Se,g,x,f,_,E),p.el=Se.el}if(ne&&Oe(ne,_),!Me&&(I=j&&j.onVnodeMounted)){const Se=p;Oe(()=>Je(I,Y,Se),_)}(p.shapeFlag&256||Y&&an(Y.vnode)&&Y.vnode.shapeFlag&256)&&f.a&&Oe(f.a,_),f.isMounted=!0,p=g=x=null}};f.scope.on();const O=f.effect=new Yo(A);f.scope.off();const R=f.update=O.run.bind(O),k=f.job=O.runIfDirty.bind(O);k.i=f,k.id=f.uid,O.scheduler=()=>yr(k),St(f,!0),R()},Z=(f,p,g)=>{p.component=f;const x=f.vnode.props;f.vnode=p,f.next=null,Wc(f,p.props,x,g),Xc(f,p.children,g),ct(),kr(f),ut()},z=(f,p,g,x,_,E,C,A,O=!1)=>{const R=f&&f.children,k=f?f.shapeFlag:0,I=p.children,{patchFlag:$,shapeFlag:j}=p;if($>0){if($&128){dt(R,I,g,x,_,E,C,A,O);return}else if($&256){et(R,I,g,x,_,E,C,A,O);return}}j&8?(k&16&&Pe(R,_,E),I!==R&&u(g,I)):k&16?j&16?dt(R,I,g,x,_,E,C,A,O):Pe(R,_,E,!0):(k&8&&u(g,""),j&16&&ge(I,g,x,_,E,C,A,O))},et=(f,p,g,x,_,E,C,A,O)=>{f=f||jt,p=p||jt;const R=f.length,k=p.length,I=Math.min(R,k);let $;for($=0;$<I;$++){const j=p[$]=O?mt(p[$]):Qe(p[$]);v(f[$],j,g,null,_,E,C,A,O)}R>k?Pe(f,_,E,!0,!1,I):ge(p,g,x,_,E,C,A,O,I)},dt=(f,p,g,x,_,E,C,A,O)=>{let R=0;const k=p.length;let I=f.length-1,$=k-1;for(;R<=I&&R<=$;){const j=f[R],K=p[R]=O?mt(p[R]):Qe(p[R]);if(Zt(j,K))v(j,K,g,null,_,E,C,A,O);else break;R++}for(;R<=I&&R<=$;){const j=f[I],K=p[$]=O?mt(p[$]):Qe(p[$]);if(Zt(j,K))v(j,K,g,null,_,E,C,A,O);else break;I--,$--}if(R>I){if(R<=$){const j=$+1,K=j<k?p[j].el:x;for(;R<=$;)v(null,p[R]=O?mt(p[R]):Qe(p[R]),g,K,_,E,C,A,O),R++}}else if(R>$)for(;R<=I;)we(f[R],_,E,!0),R++;else{const j=R,K=R,ne=new Map;for(R=K;R<=$;R++){const Re=p[R]=O?mt(p[R]):Qe(p[R]);Re.key!=null&&ne.set(Re.key,R)}let Y,ve=0;const fe=$-K+1;let Me=!1,Se=0;const Qt=new Array(fe);for(R=0;R<fe;R++)Qt[R]=0;for(R=j;R<=I;R++){const Re=f[R];if(ve>=fe){we(Re,_,E,!0);continue}let ze;if(Re.key!=null)ze=ne.get(Re.key);else for(Y=K;Y<=$;Y++)if(Qt[Y-K]===0&&Zt(Re,p[Y])){ze=Y;break}ze===void 0?we(Re,_,E,!0):(Qt[ze-K]=R+1,ze>=Se?Se=ze:Me=!0,v(Re,p[ze],g,null,_,E,C,A,O),ve++)}const Fr=Me?eu(Qt):jt;for(Y=Fr.length-1,R=fe-1;R>=0;R--){const Re=K+R,ze=p[Re],Lr=p[Re+1],Mr=Re+1<k?Lr.el||Lr.placeholder:x;Qt[R]===0?v(null,ze,g,Mr,_,E,C,A,O):Me&&(Y<0||R!==Fr[Y]?We(ze,g,Mr,2):Y--)}}},We=(f,p,g,x,_=null)=>{const{el:E,type:C,transition:A,children:O,shapeFlag:R}=f;if(R&6){We(f.component.subTree,p,g,x);return}if(R&128){f.suspense.move(p,g,x);return}if(R&64){C.move(f,p,g,D);return}if(C===rt){s(E,p,g);for(let I=0;I<O.length;I++)We(O[I],p,g,x);s(f.anchor,p,g);return}if(C===Un){F(f,p,g);return}if(x!==2&&R&1&&A)if(x===0)A.beforeEnter(E),s(E,p,g),Oe(()=>A.enter(E),_);else{const{leave:I,delayLeave:$,afterLeave:j}=A,K=()=>{f.ctx.isUnmounted?r(E):s(E,p,g)},ne=()=>{I(E,()=>{K(),j&&j()})};$?$(E,K,ne):ne()}else s(E,p,g)},we=(f,p,g,x=!1,_=!1)=>{const{type:E,props:C,ref:A,children:O,dynamicChildren:R,shapeFlag:k,patchFlag:I,dirs:$,cacheIndex:j}=f;if(I===-2&&(_=!1),A!=null&&(ct(),un(A,null,g,f,!0),ut()),j!=null&&(p.renderCache[j]=void 0),k&256){p.ctx.deactivate(f);return}const K=k&1&&$,ne=!an(f);let Y;if(ne&&(Y=C&&C.onVnodeBeforeUnmount)&&Je(Y,p,f),k&6)Pn(f.component,g,x);else{if(k&128){f.suspense.unmount(g,x);return}K&&Et(f,null,p,"beforeUnmount"),k&64?f.type.remove(f,p,g,D,x):R&&!R.hasOnce&&(E!==rt||I>0&&I&64)?Pe(R,p,g,!1,!0):(E===rt&&I&384||!_&&k&16)&&Pe(O,p,g),x&&Lt(f)}(ne&&(Y=C&&C.onVnodeUnmounted)||K)&&Oe(()=>{Y&&Je(Y,p,f),K&&Et(f,null,p,"unmounted")},g)},Lt=f=>{const{type:p,el:g,anchor:x,transition:_}=f;if(p===rt){Mt(g,x);return}if(p===Un){L(f);return}const E=()=>{r(g),_&&!_.persisted&&_.afterLeave&&_.afterLeave()};if(f.shapeFlag&1&&_&&!_.persisted){const{leave:C,delayLeave:A}=_,O=()=>C(g,E);A?A(f.el,E,O):O()}else E()},Mt=(f,p)=>{let g;for(;f!==p;)g=h(f),r(f),f=g;r(p)},Pn=(f,p,g)=>{const{bum:x,scope:_,job:E,subTree:C,um:A,m:O,a:R,parent:k,slots:{__:I}}=f;Wr(O),Wr(R),x&&Ln(x),k&&B(I)&&I.forEach($=>{k.renderCache[$]=void 0}),_.stop(),E&&(E.flags|=8,we(C,f,p,g)),A&&Oe(A,p),Oe(()=>{f.isUnmounted=!0},p),p&&p.pendingBranch&&!p.isUnmounted&&f.asyncDep&&!f.asyncResolved&&f.suspenseId===p.pendingId&&(p.deps--,p.deps===0&&p.resolve())},Pe=(f,p,g,x=!1,_=!1,E=0)=>{for(let C=E;C<f.length;C++)we(f[C],p,g,x,_)},w=f=>{if(f.shapeFlag&6)return w(f.component.subTree);if(f.shapeFlag&128)return f.suspense.next();const p=h(f.anchor||f.el),g=p&&p[xc];return g?h(g):p};let M=!1;const N=(f,p,g)=>{f==null?p._vnode&&we(p._vnode,null,null,!0):v(p._vnode||null,f,p,null,null,null,g),p._vnode=f,M||(M=!0,kr(),_i(),M=!1)},D={p:v,um:we,m:We,r:Lt,mt:Xt,mc:ge,pc:z,pbc:Ve,n:w,o:e};let J,oe;return t&&([J,oe]=t(D)),{render:N,hydrate:J,createApp:Vc(N,J)}}function Os({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function St({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Zc(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function $i(e,t,n=!1){const s=e.children,r=t.children;if(B(s)&&B(r))for(let o=0;o<s.length;o++){const i=s[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=mt(r[o]),l.el=i.el),!n&&l.patchFlag!==-2&&$i(i,l)),l.type===us&&(l.el=i.el),l.type===_t&&!l.el&&(l.el=i.el)}}function eu(e){const t=e.slice(),n=[0];let s,r,o,i,l;const c=e.length;for(s=0;s<c;s++){const a=e[s];if(a!==0){if(r=n[n.length-1],e[r]<a){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<a?o=l+1:i=l;a<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function ki(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ki(t)}function Wr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const tu=Symbol.for("v-scx"),nu=()=>je(tu);function Dn(e,t,n){return ji(e,t,n)}function ji(e,t,n=ee){const{immediate:s,deep:r,flush:o,once:i}=n,l=ae({},n),c=t&&s||!t&&o!=="post";let a;if(wn){if(o==="sync"){const m=nu();a=m.__watcherHandles||(m.__watcherHandles=[])}else if(!c){const m=()=>{};return m.stop=Ue,m.resume=Ue,m.pause=Ue,m}}const u=pe;l.call=(m,y,v)=>Ze(m,u,y,v);let d=!1;o==="post"?l.scheduler=m=>{Oe(m,u&&u.suspense)}:o!=="sync"&&(d=!0,l.scheduler=(m,y)=>{y?m():yr(m)}),l.augmentJob=m=>{t&&(m.flags|=4),d&&(m.flags|=2,u&&(m.id=u.uid,m.i=u))};const h=yc(e,t,l);return wn&&(a?a.push(h):c&&h()),h}function su(e,t,n){const s=this.proxy,r=ce(e)?e.includes(".")?Bi(s,e):()=>s[e]:e.bind(s,s);let o;H(t)?o=t:(o=t.handler,n=t);const i=On(this),l=ji(r,o.bind(s),n);return i(),l}function Bi(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const ru=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Fe(t)}Modifiers`]||e[`${It(t)}Modifiers`];function ou(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||ee;let r=n;const o=t.startsWith("update:"),i=o&&ru(s,t.slice(7));i&&(i.trim&&(r=n.map(u=>ce(u)?u.trim():u)),i.number&&(r=n.map($s)));let l,c=s[l=ws(t)]||s[l=ws(Fe(t))];!c&&o&&(c=s[l=ws(It(t))]),c&&Ze(c,e,6,r);const a=s[l+"Once"];if(a){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Ze(a,e,6,r)}}function Hi(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!H(e)){const c=a=>{const u=Hi(a,t,!0);u&&(l=!0,ae(i,u))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!o&&!l?(ie(e)&&s.set(e,null),null):(B(o)?o.forEach(c=>i[c]=null):ae(i,o),ie(e)&&s.set(e,i),i)}function cs(e,t){return!e||!es(t)?!1:(t=t.slice(2).replace(/Once$/,""),Q(e,t[0].toLowerCase()+t.slice(1))||Q(e,It(t))||Q(e,t))}function As(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:c,render:a,renderCache:u,props:d,data:h,setupState:m,ctx:y,inheritAttrs:v}=e,S=Wn(e);let P,T;try{if(n.shapeFlag&4){const L=r||s,q=L;P=Qe(a.call(q,L,u,d,m,h,y)),T=l}else{const L=t;P=Qe(L.length>1?L(d,{attrs:l,slots:i,emit:c}):L(d,null)),T=t.props?l:iu(l)}}catch(L){dn.length=0,is(L,e,1),P=re(_t)}let F=P;if(T&&v!==!1){const L=Object.keys(T),{shapeFlag:q}=F;L.length&&q&7&&(o&&L.some(ir)&&(T=lu(T,o)),F=Vt(F,T,!1,!0))}return n.dirs&&(F=Vt(F,null,!1,!0),F.dirs=F.dirs?F.dirs.concat(n.dirs):n.dirs),n.transition&&_r(F,n.transition),P=F,Wn(S),P}const iu=e=>{let t;for(const n in e)(n==="class"||n==="style"||es(n))&&((t||(t={}))[n]=e[n]);return t},lu=(e,t)=>{const n={};for(const s in e)(!ir(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function cu(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:l,patchFlag:c}=t,a=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?zr(s,i,a):!!i;if(c&8){const u=t.dynamicProps;for(let d=0;d<u.length;d++){const h=u[d];if(i[h]!==s[h]&&!cs(a,h))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?zr(s,i,a):!0:!!i;return!1}function zr(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!cs(n,o))return!0}return!1}function uu({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const qi=e=>e.__isSuspense;function au(e,t){t&&t.pendingBranch?B(e)?t.effects.push(...e):t.effects.push(e):vc(e)}const rt=Symbol.for("v-fgt"),us=Symbol.for("v-txt"),_t=Symbol.for("v-cmt"),Un=Symbol.for("v-stc"),dn=[];let Ce=null;function Ae(e=!1){dn.push(Ce=e?null:[])}function fu(){dn.pop(),Ce=dn[dn.length-1]||null}let _n=1;function Jr(e,t=!1){_n+=e,e<0&&Ce&&t&&(Ce.hasOnce=!0)}function Vi(e){return e.dynamicChildren=_n>0?Ce||jt:null,fu(),_n>0&&Ce&&Ce.push(e),e}function Ne(e,t,n,s,r,o){return Vi(U(e,t,n,s,r,o,!0))}function du(e,t,n,s,r){return Vi(re(e,t,n,s,r,!0))}function Jn(e){return e?e.__v_isVNode===!0:!1}function Zt(e,t){return e.type===t.type&&e.key===t.key}const Ki=({key:e})=>e??null,$n=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ce(e)||me(e)||H(e)?{i:Te,r:e,k:t,f:!!n}:e:null);function U(e,t=null,n=null,s=0,r=null,o=e===rt?0:1,i=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ki(t),ref:t&&$n(t),scopeId:vi,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Te};return l?(Er(c,n),o&128&&e.normalize(c)):n&&(c.shapeFlag|=ce(n)?8:16),_n>0&&!i&&Ce&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&Ce.push(c),c}const re=pu;function pu(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===Mc)&&(e=_t),Jn(e)){const l=Vt(e,t,!0);return n&&Er(l,n),_n>0&&!o&&Ce&&(l.shapeFlag&6?Ce[Ce.indexOf(e)]=l:Ce.push(l)),l.patchFlag=-2,l}if(Ou(e)&&(e=e.__vccOpts),t){t=hu(t);let{class:l,style:c}=t;l&&!ce(l)&&(t.class=Ot(l)),ie(c)&&(br(c)&&!B(c)&&(c=ae({},c)),t.style=ur(c))}const i=ce(e)?1:qi(e)?128:Ec(e)?64:ie(e)?4:H(e)?2:0;return U(e,t,n,s,r,i,o,!0)}function hu(e){return e?br(e)||Ii(e)?ae({},e):e:null}function Vt(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:c}=e,a=t?gu(r||{},t):r,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&Ki(a),ref:t&&t.ref?n&&o?B(o)?o.concat($n(t)):[o,$n(t)]:$n(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==rt?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Vt(e.ssContent),ssFallback:e.ssFallback&&Vt(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&_r(u,c.clone(u)),u}function Ie(e=" ",t=0){return re(us,null,e,t)}function mu(e,t){const n=re(Un,null,e);return n.staticCount=t,n}function Wi(e="",t=!1){return t?(Ae(),du(_t,null,e)):re(_t,null,e)}function Qe(e){return e==null||typeof e=="boolean"?re(_t):B(e)?re(rt,null,e.slice()):Jn(e)?mt(e):re(us,null,String(e))}function mt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Vt(e)}function Er(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(B(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),Er(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!Ii(t)?t._ctx=Te:r===3&&Te&&(Te.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else H(t)?(t={default:t,_ctx:Te},n=32):(t=String(t),s&64?(n=16,t=[Ie(t)]):n=8);e.children=t,e.shapeFlag|=n}function gu(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Ot([t.class,s.class]));else if(r==="style")t.style=ur([t.style,s.style]);else if(es(r)){const o=t[r],i=s[r];i&&o!==i&&!(B(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function Je(e,t,n,s=null){Ze(e,t,7,[n,s])}const bu=Ci();let yu=0;function _u(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||bu,o={uid:yu++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Kl(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Li(s,r),emitsOptions:Hi(s,r),emit:null,emitted:null,propsDefaults:ee,inheritAttrs:s.inheritAttrs,ctx:ee,data:ee,props:ee,attrs:ee,slots:ee,refs:ee,setupState:ee,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=ou.bind(null,o),e.ce&&e.ce(o),o}let pe=null;const wu=()=>pe||Te;let Gn,Js;{const e=rs(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};Gn=t("__VUE_INSTANCE_SETTERS__",n=>pe=n),Js=t("__VUE_SSR_SETTERS__",n=>wn=n)}const On=e=>{const t=pe;return Gn(e),e.scope.on(),()=>{e.scope.off(),Gn(t)}},Gr=()=>{pe&&pe.scope.off(),Gn(null)};function zi(e){return e.vnode.shapeFlag&4}let wn=!1;function vu(e,t=!1,n=!1){t&&Js(t);const{props:s,children:r}=e.vnode,o=zi(e);Kc(e,s,o,t),Gc(e,r,n||t);const i=o?xu(e,t):void 0;return t&&Js(!1),i}function xu(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Uc);const{setup:s}=n;if(s){ct();const r=e.setupContext=s.length>1?Su(e):null,o=On(e),i=Sn(s,e,0,[e.props,r]),l=Wo(i);if(ut(),o(),(l||e.sp)&&!an(e)&&xi(e),l){if(i.then(Gr,Gr),t)return i.then(c=>{Xr(e,c,t)}).catch(c=>{is(c,e,0)});e.asyncDep=i}else Xr(e,i,t)}else Ji(e,t)}function Xr(e,t,n){H(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ie(t)&&(e.setupState=mi(t)),Ji(e,n)}let Qr;function Ji(e,t,n){const s=e.type;if(!e.render){if(!t&&Qr&&!s.render){const r=s.template||wr(e).template;if(r){const{isCustomElement:o,compilerOptions:i}=e.appContext.config,{delimiters:l,compilerOptions:c}=s,a=ae(ae({isCustomElement:o,delimiters:l},i),c);s.render=Qr(r,a)}}e.render=s.render||Ue}{const r=On(e);ct();try{$c(e)}finally{ut(),r()}}}const Eu={get(e,t){return de(e,"get",""),e[t]}};function Su(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Eu),slots:e.slots,emit:e.emit,expose:t}}function as(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(mi(fc(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in fn)return fn[n](e)},has(t,n){return n in t||n in fn}})):e.proxy}function Ru(e,t=!0){return H(e)?e.displayName||e.name:e.name||t&&e.__name}function Ou(e){return H(e)&&"__vccOpts"in e}const De=(e,t)=>gc(e,t,wn);function Gi(e,t,n){const s=arguments.length;return s===2?ie(t)&&!B(t)?Jn(t)?re(e,null,[t]):re(e,t):re(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&Jn(n)&&(n=[n]),re(e,t,n))}const Au="3.5.18";/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Gs;const Yr=typeof window<"u"&&window.trustedTypes;if(Yr)try{Gs=Yr.createPolicy("vue",{createHTML:e=>e})}catch{}const Xi=Gs?e=>Gs.createHTML(e):e=>e,Tu="http://www.w3.org/2000/svg",Cu="http://www.w3.org/1998/Math/MathML",st=typeof document<"u"?document:null,Zr=st&&st.createElement("template"),Pu={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?st.createElementNS(Tu,e):t==="mathml"?st.createElementNS(Cu,e):n?st.createElement(e,{is:n}):st.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>st.createTextNode(e),createComment:e=>st.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>st.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{Zr.innerHTML=Xi(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=Zr.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Nu=Symbol("_vtc");function Iu(e,t,n){const s=e[Nu];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const eo=Symbol("_vod"),Fu=Symbol("_vsh"),Lu=Symbol(""),Mu=/(^|;)\s*display\s*:/;function Du(e,t,n){const s=e.style,r=ce(n);let o=!1;if(n&&!r){if(t)if(ce(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&kn(s,l,"")}else for(const i in t)n[i]==null&&kn(s,i,"");for(const i in n)i==="display"&&(o=!0),kn(s,i,n[i])}else if(r){if(t!==n){const i=s[Lu];i&&(n+=";"+i),s.cssText=n,o=Mu.test(n)}}else t&&e.removeAttribute("style");eo in e&&(e[eo]=o?s.display:"",e[Fu]&&(s.display="none"))}const to=/\s*!important$/;function kn(e,t,n){if(B(n))n.forEach(s=>kn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Uu(e,t);to.test(n)?e.setProperty(It(s),n.replace(to,""),"important"):e[s]=n}}const no=["Webkit","Moz","ms"],Ts={};function Uu(e,t){const n=Ts[t];if(n)return n;let s=Fe(t);if(s!=="filter"&&s in e)return Ts[t]=s;s=ss(s);for(let r=0;r<no.length;r++){const o=no[r]+s;if(o in e)return Ts[t]=o}return t}const so="http://www.w3.org/1999/xlink";function ro(e,t,n,s,r,o=Vl(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(so,t.slice(6,t.length)):e.setAttributeNS(so,t,n):n==null||o&&!Go(n)?e.removeAttribute(t):e.setAttribute(t,o?"":wt(n)?String(n):n)}function oo(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Xi(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Go(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function $t(e,t,n,s){e.addEventListener(t,n,s)}function $u(e,t,n,s){e.removeEventListener(t,n,s)}const io=Symbol("_vei");function ku(e,t,n,s,r=null){const o=e[io]||(e[io]={}),i=o[t];if(s&&i)i.value=s;else{const[l,c]=ju(t);if(s){const a=o[t]=qu(s,r);$t(e,l,a,c)}else i&&($u(e,l,i,c),o[t]=void 0)}}const lo=/(?:Once|Passive|Capture)$/;function ju(e){let t;if(lo.test(e)){t={};let s;for(;s=e.match(lo);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):It(e.slice(2)),t]}let Cs=0;const Bu=Promise.resolve(),Hu=()=>Cs||(Bu.then(()=>Cs=0),Cs=Date.now());function qu(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Ze(Vu(s,n.value),t,5,[s])};return n.value=e,n.attached=Hu(),n}function Vu(e,t){if(B(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const co=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Ku=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?Iu(e,s,i):t==="style"?Du(e,n,s):es(t)?ir(t)||ku(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Wu(e,t,s,i))?(oo(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&ro(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ce(s))?oo(e,Fe(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),ro(e,t,s,i))};function Wu(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&co(t)&&H(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return co(t)&&ce(n)?!1:t in e}const uo=e=>{const t=e.props["onUpdate:modelValue"]||!1;return B(t)?n=>Ln(t,n):t};function zu(e){e.target.composing=!0}function ao(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Ps=Symbol("_assign"),pn={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[Ps]=uo(r);const o=s||r.props&&r.props.type==="number";$t(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=$s(l)),e[Ps](l)}),n&&$t(e,"change",()=>{e.value=e.value.trim()}),t||($t(e,"compositionstart",zu),$t(e,"compositionend",ao),$t(e,"change",ao))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[Ps]=uo(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?$s(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===c)||(e.value=c))}},Ju=ae({patchProp:Ku},Pu);let fo;function Gu(){return fo||(fo=Qc(Ju))}const Xu=(...e)=>{const t=Gu().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=Yu(s);if(!r)return;const o=t._component;!H(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,Qu(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function Qu(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Yu(e){return ce(e)?document.querySelector(e):e}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const kt=typeof document<"u";function Qi(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Zu(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Qi(e.default)}const G=Object.assign;function Ns(e,t){const n={};for(const s in t){const r=t[s];n[s]=He(r)?r.map(e):e(r)}return n}const hn=()=>{},He=Array.isArray,Yi=/#/g,ea=/&/g,ta=/\//g,na=/=/g,sa=/\?/g,Zi=/\+/g,ra=/%5B/g,oa=/%5D/g,el=/%5E/g,ia=/%60/g,tl=/%7B/g,la=/%7C/g,nl=/%7D/g,ca=/%20/g;function Sr(e){return encodeURI(""+e).replace(la,"|").replace(ra,"[").replace(oa,"]")}function ua(e){return Sr(e).replace(tl,"{").replace(nl,"}").replace(el,"^")}function Xs(e){return Sr(e).replace(Zi,"%2B").replace(ca,"+").replace(Yi,"%23").replace(ea,"%26").replace(ia,"`").replace(tl,"{").replace(nl,"}").replace(el,"^")}function aa(e){return Xs(e).replace(na,"%3D")}function fa(e){return Sr(e).replace(Yi,"%23").replace(sa,"%3F")}function da(e){return e==null?"":fa(e).replace(ta,"%2F")}function vn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const pa=/\/$/,ha=e=>e.replace(pa,"");function Is(e,t,n="/"){let s,r={},o="",i="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),o=t.slice(c+1,l>-1?l:t.length),r=e(o)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=ya(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:vn(i)}}function ma(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function po(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function ga(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Kt(t.matched[s],n.matched[r])&&sl(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Kt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function sl(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!ba(e[n],t[n]))return!1;return!0}function ba(e,t){return He(e)?ho(e,t):He(t)?ho(t,e):e===t}function ho(e,t){return He(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function ya(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const pt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var xn;(function(e){e.pop="pop",e.push="push"})(xn||(xn={}));var mn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(mn||(mn={}));function _a(e){if(!e)if(kt){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),ha(e)}const wa=/^[^#]+#/;function va(e,t){return e.replace(wa,"#")+t}function xa(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const fs=()=>({left:window.scrollX,top:window.scrollY});function Ea(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=xa(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function mo(e,t){return(history.state?history.state.position-t:-1)+e}const Qs=new Map;function Sa(e,t){Qs.set(e,t)}function Ra(e){const t=Qs.get(e);return Qs.delete(e),t}let Oa=()=>location.protocol+"//"+location.host;function rl(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,c=r.slice(l);return c[0]!=="/"&&(c="/"+c),po(c,"")}return po(n,e)+s+r}function Aa(e,t,n,s){let r=[],o=[],i=null;const l=({state:h})=>{const m=rl(e,location),y=n.value,v=t.value;let S=0;if(h){if(n.value=m,t.value=h,i&&i===y){i=null;return}S=v?h.position-v.position:0}else s(m);r.forEach(P=>{P(n.value,y,{delta:S,type:xn.pop,direction:S?S>0?mn.forward:mn.back:mn.unknown})})};function c(){i=n.value}function a(h){r.push(h);const m=()=>{const y=r.indexOf(h);y>-1&&r.splice(y,1)};return o.push(m),m}function u(){const{history:h}=window;h.state&&h.replaceState(G({},h.state,{scroll:fs()}),"")}function d(){for(const h of o)h();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",u)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",u,{passive:!0}),{pauseListeners:c,listen:a,destroy:d}}function go(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?fs():null}}function Ta(e){const{history:t,location:n}=window,s={value:rl(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(c,a,u){const d=e.indexOf("#"),h=d>-1?(n.host&&document.querySelector("base")?e:e.slice(d))+c:Oa()+e+c;try{t[u?"replaceState":"pushState"](a,"",h),r.value=a}catch(m){console.error(m),n[u?"replace":"assign"](h)}}function i(c,a){const u=G({},t.state,go(r.value.back,c,r.value.forward,!0),a,{position:r.value.position});o(c,u,!0),s.value=c}function l(c,a){const u=G({},r.value,t.state,{forward:c,scroll:fs()});o(u.current,u,!0);const d=G({},go(s.value,c,null),{position:u.position+1},a);o(c,d,!1),s.value=c}return{location:s,state:r,push:l,replace:i}}function Ca(e){e=_a(e);const t=Ta(e),n=Aa(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=G({location:"",base:e,go:s,createHref:va.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Pa(e){return typeof e=="string"||e&&typeof e=="object"}function ol(e){return typeof e=="string"||typeof e=="symbol"}const il=Symbol("");var bo;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(bo||(bo={}));function Wt(e,t){return G(new Error,{type:e,[il]:!0},t)}function nt(e,t){return e instanceof Error&&il in e&&(t==null||!!(e.type&t))}const yo="[^/]+?",Na={sensitive:!1,strict:!1,start:!0,end:!0},Ia=/[.+*?^${}()[\]/\\]/g;function Fa(e,t){const n=G({},Na,t),s=[];let r=n.start?"^":"";const o=[];for(const a of e){const u=a.length?[]:[90];n.strict&&!a.length&&(r+="/");for(let d=0;d<a.length;d++){const h=a[d];let m=40+(n.sensitive?.25:0);if(h.type===0)d||(r+="/"),r+=h.value.replace(Ia,"\\$&"),m+=40;else if(h.type===1){const{value:y,repeatable:v,optional:S,regexp:P}=h;o.push({name:y,repeatable:v,optional:S});const T=P||yo;if(T!==yo){m+=10;try{new RegExp(`(${T})`)}catch(L){throw new Error(`Invalid custom RegExp for param "${y}" (${T}): `+L.message)}}let F=v?`((?:${T})(?:/(?:${T}))*)`:`(${T})`;d||(F=S&&a.length<2?`(?:/${F})`:"/"+F),S&&(F+="?"),r+=F,m+=20,S&&(m+=-8),v&&(m+=-20),T===".*"&&(m+=-50)}u.push(m)}s.push(u)}if(n.strict&&n.end){const a=s.length-1;s[a][s[a].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(a){const u=a.match(i),d={};if(!u)return null;for(let h=1;h<u.length;h++){const m=u[h]||"",y=o[h-1];d[y.name]=m&&y.repeatable?m.split("/"):m}return d}function c(a){let u="",d=!1;for(const h of e){(!d||!u.endsWith("/"))&&(u+="/"),d=!1;for(const m of h)if(m.type===0)u+=m.value;else if(m.type===1){const{value:y,repeatable:v,optional:S}=m,P=y in a?a[y]:"";if(He(P)&&!v)throw new Error(`Provided param "${y}" is an array but it is not repeatable (* or + modifiers)`);const T=He(P)?P.join("/"):P;if(!T)if(S)h.length<2&&(u.endsWith("/")?u=u.slice(0,-1):d=!0);else throw new Error(`Missing required param "${y}"`);u+=T}}return u||"/"}return{re:i,score:s,keys:o,parse:l,stringify:c}}function La(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function ll(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=La(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(_o(s))return 1;if(_o(r))return-1}return r.length-s.length}function _o(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Ma={type:0,value:""},Da=/[a-zA-Z0-9_]/;function Ua(e){if(!e)return[[]];if(e==="/")return[[Ma]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(m){throw new Error(`ERR (${n})/"${a}": ${m}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,c,a="",u="";function d(){a&&(n===0?o.push({type:0,value:a}):n===1||n===2||n===3?(o.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${a}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:a,regexp:u,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),a="")}function h(){a+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(a&&d(),i()):c===":"?(d(),n=1):h();break;case 4:h(),n=s;break;case 1:c==="("?n=2:Da.test(c)?h():(d(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?u[u.length-1]=="\\"?u=u.slice(0,-1)+c:n=3:u+=c;break;case 3:d(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,u="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${a}"`),d(),i(),r}function $a(e,t,n){const s=Fa(Ua(e.path),n),r=G(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function ka(e,t){const n=[],s=new Map;t=Eo({strict:!1,end:!0,sensitive:!1},t);function r(d){return s.get(d)}function o(d,h,m){const y=!m,v=vo(d);v.aliasOf=m&&m.record;const S=Eo(t,d),P=[v];if("alias"in d){const L=typeof d.alias=="string"?[d.alias]:d.alias;for(const q of L)P.push(vo(G({},v,{components:m?m.record.components:v.components,path:q,aliasOf:m?m.record:v})))}let T,F;for(const L of P){const{path:q}=L;if(h&&q[0]!=="/"){const te=h.record.path,W=te[te.length-1]==="/"?"":"/";L.path=h.record.path+(q&&W+q)}if(T=$a(L,h,S),m?m.alias.push(T):(F=F||T,F!==T&&F.alias.push(T),y&&d.name&&!xo(T)&&i(d.name)),cl(T)&&c(T),v.children){const te=v.children;for(let W=0;W<te.length;W++)o(te[W],T,m&&m.children[W])}m=m||T}return F?()=>{i(F)}:hn}function i(d){if(ol(d)){const h=s.get(d);h&&(s.delete(d),n.splice(n.indexOf(h),1),h.children.forEach(i),h.alias.forEach(i))}else{const h=n.indexOf(d);h>-1&&(n.splice(h,1),d.record.name&&s.delete(d.record.name),d.children.forEach(i),d.alias.forEach(i))}}function l(){return n}function c(d){const h=Ha(d,n);n.splice(h,0,d),d.record.name&&!xo(d)&&s.set(d.record.name,d)}function a(d,h){let m,y={},v,S;if("name"in d&&d.name){if(m=s.get(d.name),!m)throw Wt(1,{location:d});S=m.record.name,y=G(wo(h.params,m.keys.filter(F=>!F.optional).concat(m.parent?m.parent.keys.filter(F=>F.optional):[]).map(F=>F.name)),d.params&&wo(d.params,m.keys.map(F=>F.name))),v=m.stringify(y)}else if(d.path!=null)v=d.path,m=n.find(F=>F.re.test(v)),m&&(y=m.parse(v),S=m.record.name);else{if(m=h.name?s.get(h.name):n.find(F=>F.re.test(h.path)),!m)throw Wt(1,{location:d,currentLocation:h});S=m.record.name,y=G({},h.params,d.params),v=m.stringify(y)}const P=[];let T=m;for(;T;)P.unshift(T.record),T=T.parent;return{name:S,path:v,params:y,matched:P,meta:Ba(P)}}e.forEach(d=>o(d));function u(){n.length=0,s.clear()}return{addRoute:o,resolve:a,removeRoute:i,clearRoutes:u,getRoutes:l,getRecordMatcher:r}}function wo(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function vo(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:ja(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function ja(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function xo(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Ba(e){return e.reduce((t,n)=>G(t,n.meta),{})}function Eo(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function Ha(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;ll(e,t[o])<0?s=o:n=o+1}const r=qa(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function qa(e){let t=e;for(;t=t.parent;)if(cl(t)&&ll(e,t)===0)return t}function cl({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Va(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(Zi," "),i=o.indexOf("="),l=vn(i<0?o:o.slice(0,i)),c=i<0?null:vn(o.slice(i+1));if(l in t){let a=t[l];He(a)||(a=t[l]=[a]),a.push(c)}else t[l]=c}return t}function So(e){let t="";for(let n in e){const s=e[n];if(n=aa(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(He(s)?s.map(o=>o&&Xs(o)):[s&&Xs(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function Ka(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=He(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const Wa=Symbol(""),Ro=Symbol(""),ds=Symbol(""),Rr=Symbol(""),Ys=Symbol("");function en(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function gt(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,c)=>{const a=h=>{h===!1?c(Wt(4,{from:n,to:t})):h instanceof Error?c(h):Pa(h)?c(Wt(2,{from:t,to:h})):(i&&s.enterCallbacks[r]===i&&typeof h=="function"&&i.push(h),l())},u=o(()=>e.call(s&&s.instances[r],t,n,a));let d=Promise.resolve(u);e.length<3&&(d=d.then(a)),d.catch(h=>c(h))})}function Fs(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let c=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(Qi(c)){const u=(c.__vccOpts||c)[t];u&&o.push(gt(u,n,s,i,l,r))}else{let a=c();o.push(()=>a.then(u=>{if(!u)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const d=Zu(u)?u.default:u;i.mods[l]=u,i.components[l]=d;const m=(d.__vccOpts||d)[t];return m&&gt(m,n,s,i,l,r)()}))}}return o}function Oo(e){const t=je(ds),n=je(Rr),s=De(()=>{const c=Ct(e.to);return t.resolve(c)}),r=De(()=>{const{matched:c}=s.value,{length:a}=c,u=c[a-1],d=n.matched;if(!u||!d.length)return-1;const h=d.findIndex(Kt.bind(null,u));if(h>-1)return h;const m=Ao(c[a-2]);return a>1&&Ao(u)===m&&d[d.length-1].path!==m?d.findIndex(Kt.bind(null,c[a-2])):h}),o=De(()=>r.value>-1&&Qa(n.params,s.value.params)),i=De(()=>r.value>-1&&r.value===n.matched.length-1&&sl(n.params,s.value.params));function l(c={}){if(Xa(c)){const a=t[Ct(e.replace)?"replace":"push"](Ct(e.to)).catch(hn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>a),a}return Promise.resolve()}return{route:s,href:De(()=>s.value.href),isActive:o,isExactActive:i,navigate:l}}function za(e){return e.length===1?e[0]:e}const Ja=vt({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Oo,setup(e,{slots:t}){const n=os(Oo(e)),{options:s}=je(ds),r=De(()=>({[To(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[To(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&za(t.default(n));return e.custom?o:Gi("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),Ga=Ja;function Xa(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Qa(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!He(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function Ao(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const To=(e,t,n)=>e??t??n,Ya=vt({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=je(Ys),r=De(()=>e.route||s.value),o=je(Ro,0),i=De(()=>{let a=Ct(o);const{matched:u}=r.value;let d;for(;(d=u[a])&&!d.components;)a++;return a}),l=De(()=>r.value.matched[i.value]);Mn(Ro,De(()=>i.value+1)),Mn(Wa,l),Mn(Ys,r);const c=lt();return Dn(()=>[c.value,l.value,e.name],([a,u,d],[h,m,y])=>{u&&(u.instances[d]=a,m&&m!==u&&a&&a===h&&(u.leaveGuards.size||(u.leaveGuards=m.leaveGuards),u.updateGuards.size||(u.updateGuards=m.updateGuards))),a&&u&&(!m||!Kt(u,m)||!h)&&(u.enterCallbacks[d]||[]).forEach(v=>v(a))},{flush:"post"}),()=>{const a=r.value,u=e.name,d=l.value,h=d&&d.components[u];if(!h)return Co(n.default,{Component:h,route:a});const m=d.props[u],y=m?m===!0?a.params:typeof m=="function"?m(a):m:null,S=Gi(h,G({},y,t,{onVnodeUnmounted:P=>{P.component.isUnmounted&&(d.instances[u]=null)},ref:c}));return Co(n.default,{Component:S,route:a})||S}}});function Co(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const Za=Ya;function ef(e){const t=ka(e.routes,e),n=e.parseQuery||Va,s=e.stringifyQuery||So,r=e.history,o=en(),i=en(),l=en(),c=dc(pt);let a=pt;kt&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const u=Ns.bind(null,w=>""+w),d=Ns.bind(null,da),h=Ns.bind(null,vn);function m(w,M){let N,D;return ol(w)?(N=t.getRecordMatcher(w),D=M):D=w,t.addRoute(D,N)}function y(w){const M=t.getRecordMatcher(w);M&&t.removeRoute(M)}function v(){return t.getRoutes().map(w=>w.record)}function S(w){return!!t.getRecordMatcher(w)}function P(w,M){if(M=G({},M||c.value),typeof w=="string"){const p=Is(n,w,M.path),g=t.resolve({path:p.path},M),x=r.createHref(p.fullPath);return G(p,g,{params:h(g.params),hash:vn(p.hash),redirectedFrom:void 0,href:x})}let N;if(w.path!=null)N=G({},w,{path:Is(n,w.path,M.path).path});else{const p=G({},w.params);for(const g in p)p[g]==null&&delete p[g];N=G({},w,{params:d(p)}),M.params=d(M.params)}const D=t.resolve(N,M),J=w.hash||"";D.params=u(h(D.params));const oe=ma(s,G({},w,{hash:ua(J),path:D.path})),f=r.createHref(oe);return G({fullPath:oe,hash:J,query:s===So?Ka(w.query):w.query||{}},D,{redirectedFrom:void 0,href:f})}function T(w){return typeof w=="string"?Is(n,w,c.value.path):G({},w)}function F(w,M){if(a!==w)return Wt(8,{from:M,to:w})}function L(w){return W(w)}function q(w){return L(G(T(w),{replace:!0}))}function te(w){const M=w.matched[w.matched.length-1];if(M&&M.redirect){const{redirect:N}=M;let D=typeof N=="function"?N(w):N;return typeof D=="string"&&(D=D.includes("?")||D.includes("#")?D=T(D):{path:D},D.params={}),G({query:w.query,hash:w.hash,params:D.path!=null?{}:w.params},D)}}function W(w,M){const N=a=P(w),D=c.value,J=w.state,oe=w.force,f=w.replace===!0,p=te(N);if(p)return W(G(T(p),{state:typeof p=="object"?G({},J,p.state):J,force:oe,replace:f}),M||N);const g=N;g.redirectedFrom=M;let x;return!oe&&ga(s,D,N)&&(x=Wt(16,{to:g,from:D}),We(D,D,!0,!1)),(x?Promise.resolve(x):Ve(g,D)).catch(_=>nt(_)?nt(_,2)?_:dt(_):z(_,g,D)).then(_=>{if(_){if(nt(_,2))return W(G({replace:f},T(_.to),{state:typeof _.to=="object"?G({},J,_.to.state):J,force:oe}),M||g)}else _=xt(g,D,!0,f,J);return ft(g,D,_),_})}function ge(w,M){const N=F(w,M);return N?Promise.reject(N):Promise.resolve()}function Le(w){const M=Mt.values().next().value;return M&&typeof M.runWithContext=="function"?M.runWithContext(w):w()}function Ve(w,M){let N;const[D,J,oe]=tf(w,M);N=Fs(D.reverse(),"beforeRouteLeave",w,M);for(const p of D)p.leaveGuards.forEach(g=>{N.push(gt(g,w,M))});const f=ge.bind(null,w,M);return N.push(f),Pe(N).then(()=>{N=[];for(const p of o.list())N.push(gt(p,w,M));return N.push(f),Pe(N)}).then(()=>{N=Fs(J,"beforeRouteUpdate",w,M);for(const p of J)p.updateGuards.forEach(g=>{N.push(gt(g,w,M))});return N.push(f),Pe(N)}).then(()=>{N=[];for(const p of oe)if(p.beforeEnter)if(He(p.beforeEnter))for(const g of p.beforeEnter)N.push(gt(g,w,M));else N.push(gt(p.beforeEnter,w,M));return N.push(f),Pe(N)}).then(()=>(w.matched.forEach(p=>p.enterCallbacks={}),N=Fs(oe,"beforeRouteEnter",w,M,Le),N.push(f),Pe(N))).then(()=>{N=[];for(const p of i.list())N.push(gt(p,w,M));return N.push(f),Pe(N)}).catch(p=>nt(p,8)?p:Promise.reject(p))}function ft(w,M,N){l.list().forEach(D=>Le(()=>D(w,M,N)))}function xt(w,M,N,D,J){const oe=F(w,M);if(oe)return oe;const f=M===pt,p=kt?history.state:{};N&&(D||f?r.replace(w.fullPath,G({scroll:f&&p&&p.scroll},J)):r.push(w.fullPath,J)),c.value=w,We(w,M,N,f),dt()}let Ke;function Xt(){Ke||(Ke=r.listen((w,M,N)=>{if(!Pn.listening)return;const D=P(w),J=te(D);if(J){W(G(J,{replace:!0,force:!0}),D).catch(hn);return}a=D;const oe=c.value;kt&&Sa(mo(oe.fullPath,N.delta),fs()),Ve(D,oe).catch(f=>nt(f,12)?f:nt(f,2)?(W(G(T(f.to),{force:!0}),D).then(p=>{nt(p,20)&&!N.delta&&N.type===xn.pop&&r.go(-1,!1)}).catch(hn),Promise.reject()):(N.delta&&r.go(-N.delta,!1),z(f,D,oe))).then(f=>{f=f||xt(D,oe,!1),f&&(N.delta&&!nt(f,8)?r.go(-N.delta,!1):N.type===xn.pop&&nt(f,20)&&r.go(-1,!1)),ft(D,oe,f)}).catch(hn)}))}let Ft=en(),ue=en(),Z;function z(w,M,N){dt(w);const D=ue.list();return D.length?D.forEach(J=>J(w,M,N)):console.error(w),Promise.reject(w)}function et(){return Z&&c.value!==pt?Promise.resolve():new Promise((w,M)=>{Ft.add([w,M])})}function dt(w){return Z||(Z=!w,Xt(),Ft.list().forEach(([M,N])=>w?N(w):M()),Ft.reset()),w}function We(w,M,N,D){const{scrollBehavior:J}=e;if(!kt||!J)return Promise.resolve();const oe=!N&&Ra(mo(w.fullPath,0))||(D||!N)&&history.state&&history.state.scroll||null;return bi().then(()=>J(w,M,oe)).then(f=>f&&Ea(f)).catch(f=>z(f,w,M))}const we=w=>r.go(w);let Lt;const Mt=new Set,Pn={currentRoute:c,listening:!0,addRoute:m,removeRoute:y,clearRoutes:t.clearRoutes,hasRoute:S,getRoutes:v,resolve:P,options:e,push:L,replace:q,go:we,back:()=>we(-1),forward:()=>we(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:ue.add,isReady:et,install(w){const M=this;w.component("RouterLink",Ga),w.component("RouterView",Za),w.config.globalProperties.$router=M,Object.defineProperty(w.config.globalProperties,"$route",{enumerable:!0,get:()=>Ct(c)}),kt&&!Lt&&c.value===pt&&(Lt=!0,L(r.location).catch(J=>{}));const N={};for(const J in pt)Object.defineProperty(N,J,{get:()=>c.value[J],enumerable:!0});w.provide(ds,M),w.provide(Rr,di(N)),w.provide(Ys,c);const D=w.unmount;Mt.add(w),w.unmount=function(){Mt.delete(w),Mt.size<1&&(a=pt,Ke&&Ke(),Ke=null,c.value=pt,Lt=!1,Z=!1),D()}}};function Pe(w){return w.reduce((M,N)=>M.then(()=>Le(N)),Promise.resolve())}return Pn}function tf(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(a=>Kt(a,l))?s.push(l):n.push(l));const c=e.matched[i];c&&(t.matched.find(a=>Kt(a,c))||r.push(c))}return[n,s,r]}function Or(){return je(ds)}function nf(e){return je(Rr)}const sf={class:"bg-white shadow-md"},rf={class:"container mx-auto px-4"},of={class:"flex justify-between items-center h-16"},lf={class:"flex items-center"},cf={class:"flex space-x-8"},uf=vt({__name:"Navbar",setup(e){const t=nf(),n=s=>t.path===s;return(s,r)=>{const o=Rn("router-link");return Ae(),Ne("nav",sf,[U("div",rf,[U("div",of,[U("div",lf,[re(o,{to:"/",class:"text-xl font-bold text-blue-600"},{default:Ye(()=>r[0]||(r[0]=[Ie(" MyProject ",-1)])),_:1,__:[0]})]),U("div",cf,[re(o,{to:"/",class:Ot([n("/")?"text-blue-600 border-b-2 border-blue-600":"text-gray-600 hover:text-blue-500","px-1 py-2 transition-colors"])},{default:Ye(()=>r[1]||(r[1]=[Ie(" Home ",-1)])),_:1,__:[1]},8,["class"]),re(o,{to:"/login",class:Ot([n("/login")?"text-blue-600 border-b-2 border-blue-600":"text-gray-600 hover:text-blue-500","px-1 py-2 transition-colors"])},{default:Ye(()=>r[2]||(r[2]=[Ie(" Login ",-1)])),_:1,__:[2]},8,["class"]),re(o,{to:"/register",class:Ot([n("/register")?"text-blue-600 border-b-2 border-blue-600":"text-gray-600 hover:text-blue-500","px-1 py-2 transition-colors"])},{default:Ye(()=>r[3]||(r[3]=[Ie(" Register ",-1)])),_:1,__:[3]},8,["class"]),re(o,{to:"/profile",class:Ot([n("/profile")?"text-blue-600 border-b-2 border-blue-600":"text-gray-600 hover:text-blue-500","px-1 py-2 transition-colors"])},{default:Ye(()=>r[4]||(r[4]=[Ie(" Profile ",-1)])),_:1,__:[4]},8,["class"])])])])])}}});const zt=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},af=zt(uf,[["__scopeId","data-v-7328849c"]]),ff={class:"min-h-screen bg-gray-50"},df={class:"container mx-auto px-4 py-8"},pf=vt({__name:"App",setup(e){return(t,n)=>{const s=Rn("router-view");return Ae(),Ne("div",ff,[re(af),U("main",df,[re(s)])])}}});const hf=zt(pf,[["__scopeId","data-v-19bab19e"]]),mf={class:"max-w-4xl mx-auto"},gf={class:"text-center py-12"},bf={class:"flex justify-center gap-4"},yf=vt({__name:"Home",setup(e){return(t,n)=>{const s=Rn("router-link");return Ae(),Ne("div",mf,[U("div",gf,[n[2]||(n[2]=U("h1",{class:"text-4xl font-bold text-gray-900 mb-4"}," Welcome to MyProject ",-1)),n[3]||(n[3]=U("p",{class:"text-xl text-gray-600 mb-8"}," A modern web application built with cutting-edge technologies ",-1)),U("div",bf,[re(s,{to:"/login",class:"px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"},{default:Ye(()=>n[0]||(n[0]=[Ie(" Login ",-1)])),_:1,__:[0]}),re(s,{to:"/register",class:"px-6 py-3 bg-white text-blue-600 font-medium rounded-lg border border-blue-600 hover:bg-blue-50 transition-colors"},{default:Ye(()=>n[1]||(n[1]=[Ie(" Register ",-1)])),_:1,__:[1]})])]),n[4]||(n[4]=mu('<div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16" data-v-cc82be80><div class="bg-white p-6 rounded-lg shadow-md" data-v-cc82be80><h3 class="text-xl font-semibold mb-2" data-v-cc82be80>Modern Architecture</h3><p class="text-gray-600" data-v-cc82be80> Built with a scalable architecture that supports microservices and cloud deployment. </p></div><div class="bg-white p-6 rounded-lg shadow-md" data-v-cc82be80><h3 class="text-xl font-semibold mb-2" data-v-cc82be80>Secure Authentication</h3><p class="text-gray-600" data-v-cc82be80> Robust authentication system with JWT tokens and secure password handling. </p></div><div class="bg-white p-6 rounded-lg shadow-md" data-v-cc82be80><h3 class="text-xl font-semibold mb-2" data-v-cc82be80>Responsive Design</h3><p class="text-gray-600" data-v-cc82be80> Fully responsive interface that works on all devices from mobile to desktop. </p></div></div>',1))])}}});const _f=zt(yf,[["__scopeId","data-v-cc82be80"]]);function ul(e,t){return function(){return e.apply(t,arguments)}}const{toString:wf}=Object.prototype,{getPrototypeOf:Ar}=Object,{iterator:ps,toStringTag:al}=Symbol,hs=(e=>t=>{const n=wf.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),qe=e=>(e=e.toLowerCase(),t=>hs(t)===e),ms=e=>t=>typeof t===e,{isArray:Jt}=Array,En=ms("undefined");function An(e){return e!==null&&!En(e)&&e.constructor!==null&&!En(e.constructor)&&Ee(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const fl=qe("ArrayBuffer");function vf(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&fl(e.buffer),t}const xf=ms("string"),Ee=ms("function"),dl=ms("number"),Tn=e=>e!==null&&typeof e=="object",Ef=e=>e===!0||e===!1,jn=e=>{if(hs(e)!=="object")return!1;const t=Ar(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(al in e)&&!(ps in e)},Sf=e=>{if(!Tn(e)||An(e))return!1;try{return Object.keys(e).length===0&&Object.getPrototypeOf(e)===Object.prototype}catch{return!1}},Rf=qe("Date"),Of=qe("File"),Af=qe("Blob"),Tf=qe("FileList"),Cf=e=>Tn(e)&&Ee(e.pipe),Pf=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||Ee(e.append)&&((t=hs(e))==="formdata"||t==="object"&&Ee(e.toString)&&e.toString()==="[object FormData]"))},Nf=qe("URLSearchParams"),[If,Ff,Lf,Mf]=["ReadableStream","Request","Response","Headers"].map(qe),Df=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function Cn(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,r;if(typeof e!="object"&&(e=[e]),Jt(e))for(s=0,r=e.length;s<r;s++)t.call(null,e[s],s,e);else{if(An(e))return;const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let l;for(s=0;s<i;s++)l=o[s],t.call(null,e[l],l,e)}}function pl(e,t){if(An(e))return null;t=t.toLowerCase();const n=Object.keys(e);let s=n.length,r;for(;s-- >0;)if(r=n[s],t===r.toLowerCase())return r;return null}const At=(()=>typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global)(),hl=e=>!En(e)&&e!==At;function Zs(){const{caseless:e}=hl(this)&&this||{},t={},n=(s,r)=>{const o=e&&pl(t,r)||r;jn(t[o])&&jn(s)?t[o]=Zs(t[o],s):jn(s)?t[o]=Zs({},s):Jt(s)?t[o]=s.slice():t[o]=s};for(let s=0,r=arguments.length;s<r;s++)arguments[s]&&Cn(arguments[s],n);return t}const Uf=(e,t,n,{allOwnKeys:s}={})=>(Cn(t,(r,o)=>{n&&Ee(r)?e[o]=ul(r,n):e[o]=r},{allOwnKeys:s}),e),$f=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),kf=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},jf=(e,t,n,s)=>{let r,o,i;const l={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),o=r.length;o-- >0;)i=r[o],(!s||s(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=n!==!1&&Ar(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Bf=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},Hf=e=>{if(!e)return null;if(Jt(e))return e;let t=e.length;if(!dl(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},qf=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Ar(Uint8Array)),Vf=(e,t)=>{const s=(e&&e[ps]).call(e);let r;for(;(r=s.next())&&!r.done;){const o=r.value;t.call(e,o[0],o[1])}},Kf=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},Wf=qe("HTMLFormElement"),zf=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,r){return s.toUpperCase()+r}),Po=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Jf=qe("RegExp"),ml=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};Cn(n,(r,o)=>{let i;(i=t(r,o,e))!==!1&&(s[o]=i||r)}),Object.defineProperties(e,s)},Gf=e=>{ml(e,(t,n)=>{if(Ee(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(Ee(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Xf=(e,t)=>{const n={},s=r=>{r.forEach(o=>{n[o]=!0})};return Jt(e)?s(e):s(String(e).split(t)),n},Qf=()=>{},Yf=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Zf(e){return!!(e&&Ee(e.append)&&e[al]==="FormData"&&e[ps])}const ed=e=>{const t=new Array(10),n=(s,r)=>{if(Tn(s)){if(t.indexOf(s)>=0)return;if(An(s))return s;if(!("toJSON"in s)){t[r]=s;const o=Jt(s)?[]:{};return Cn(s,(i,l)=>{const c=n(i,r+1);!En(c)&&(o[l]=c)}),t[r]=void 0,o}}return s};return n(e,0)},td=qe("AsyncFunction"),nd=e=>e&&(Tn(e)||Ee(e))&&Ee(e.then)&&Ee(e.catch),gl=((e,t)=>e?setImmediate:t?((n,s)=>(At.addEventListener("message",({source:r,data:o})=>{r===At&&o===n&&s.length&&s.shift()()},!1),r=>{s.push(r),At.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",Ee(At.postMessage)),sd=typeof queueMicrotask<"u"?queueMicrotask.bind(At):typeof process<"u"&&process.nextTick||gl,rd=e=>e!=null&&Ee(e[ps]),b={isArray:Jt,isArrayBuffer:fl,isBuffer:An,isFormData:Pf,isArrayBufferView:vf,isString:xf,isNumber:dl,isBoolean:Ef,isObject:Tn,isPlainObject:jn,isEmptyObject:Sf,isReadableStream:If,isRequest:Ff,isResponse:Lf,isHeaders:Mf,isUndefined:En,isDate:Rf,isFile:Of,isBlob:Af,isRegExp:Jf,isFunction:Ee,isStream:Cf,isURLSearchParams:Nf,isTypedArray:qf,isFileList:Tf,forEach:Cn,merge:Zs,extend:Uf,trim:Df,stripBOM:$f,inherits:kf,toFlatObject:jf,kindOf:hs,kindOfTest:qe,endsWith:Bf,toArray:Hf,forEachEntry:Vf,matchAll:Kf,isHTMLForm:Wf,hasOwnProperty:Po,hasOwnProp:Po,reduceDescriptors:ml,freezeMethods:Gf,toObjectSet:Xf,toCamelCase:zf,noop:Qf,toFiniteNumber:Yf,findKey:pl,global:At,isContextDefined:hl,isSpecCompliantForm:Zf,toJSONObject:ed,isAsyncFn:td,isThenable:nd,setImmediate:gl,asap:sd,isIterable:rd};function V(e,t,n,s,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),r&&(this.response=r,this.status=r.status?r.status:null)}b.inherits(V,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:b.toJSONObject(this.config),code:this.code,status:this.status}}});const bl=V.prototype,yl={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{yl[e]={value:e}});Object.defineProperties(V,yl);Object.defineProperty(bl,"isAxiosError",{value:!0});V.from=(e,t,n,s,r,o)=>{const i=Object.create(bl);return b.toFlatObject(e,i,function(c){return c!==Error.prototype},l=>l!=="isAxiosError"),V.call(i,e.message,t,n,s,r),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const od=null;function er(e){return b.isPlainObject(e)||b.isArray(e)}function _l(e){return b.endsWith(e,"[]")?e.slice(0,-2):e}function No(e,t,n){return e?e.concat(t).map(function(r,o){return r=_l(r),!n&&o?"["+r+"]":r}).join(n?".":""):t}function id(e){return b.isArray(e)&&!e.some(er)}const ld=b.toFlatObject(b,{},null,function(t){return/^is[A-Z]/.test(t)});function gs(e,t,n){if(!b.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=b.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(v,S){return!b.isUndefined(S[v])});const s=n.metaTokens,r=n.visitor||u,o=n.dots,i=n.indexes,c=(n.Blob||typeof Blob<"u"&&Blob)&&b.isSpecCompliantForm(t);if(!b.isFunction(r))throw new TypeError("visitor must be a function");function a(y){if(y===null)return"";if(b.isDate(y))return y.toISOString();if(b.isBoolean(y))return y.toString();if(!c&&b.isBlob(y))throw new V("Blob is not supported. Use a Buffer instead.");return b.isArrayBuffer(y)||b.isTypedArray(y)?c&&typeof Blob=="function"?new Blob([y]):Buffer.from(y):y}function u(y,v,S){let P=y;if(y&&!S&&typeof y=="object"){if(b.endsWith(v,"{}"))v=s?v:v.slice(0,-2),y=JSON.stringify(y);else if(b.isArray(y)&&id(y)||(b.isFileList(y)||b.endsWith(v,"[]"))&&(P=b.toArray(y)))return v=_l(v),P.forEach(function(F,L){!(b.isUndefined(F)||F===null)&&t.append(i===!0?No([v],L,o):i===null?v:v+"[]",a(F))}),!1}return er(y)?!0:(t.append(No(S,v,o),a(y)),!1)}const d=[],h=Object.assign(ld,{defaultVisitor:u,convertValue:a,isVisitable:er});function m(y,v){if(!b.isUndefined(y)){if(d.indexOf(y)!==-1)throw Error("Circular reference detected in "+v.join("."));d.push(y),b.forEach(y,function(P,T){(!(b.isUndefined(P)||P===null)&&r.call(t,P,b.isString(T)?T.trim():T,v,h))===!0&&m(P,v?v.concat(T):[T])}),d.pop()}}if(!b.isObject(e))throw new TypeError("data must be an object");return m(e),t}function Io(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function Tr(e,t){this._pairs=[],e&&gs(e,this,t)}const wl=Tr.prototype;wl.append=function(t,n){this._pairs.push([t,n])};wl.toString=function(t){const n=t?function(s){return t.call(this,s,Io)}:Io;return this._pairs.map(function(r){return n(r[0])+"="+n(r[1])},"").join("&")};function cd(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function vl(e,t,n){if(!t)return e;const s=n&&n.encode||cd;b.isFunction(n)&&(n={serialize:n});const r=n&&n.serialize;let o;if(r?o=r(t,n):o=b.isURLSearchParams(t)?t.toString():new Tr(t,n).toString(s),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class ud{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){b.forEach(this.handlers,function(s){s!==null&&t(s)})}}const Fo=ud,xl={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ad=typeof URLSearchParams<"u"?URLSearchParams:Tr,fd=typeof FormData<"u"?FormData:null,dd=typeof Blob<"u"?Blob:null,pd={isBrowser:!0,classes:{URLSearchParams:ad,FormData:fd,Blob:dd},protocols:["http","https","file","blob","url","data"]},Cr=typeof window<"u"&&typeof document<"u",tr=typeof navigator=="object"&&navigator||void 0,hd=Cr&&(!tr||["ReactNative","NativeScript","NS"].indexOf(tr.product)<0),md=(()=>typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function")(),gd=Cr&&window.location.href||"http://localhost",bd=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Cr,hasStandardBrowserEnv:hd,hasStandardBrowserWebWorkerEnv:md,navigator:tr,origin:gd},Symbol.toStringTag,{value:"Module"})),he={...bd,...pd};function yd(e,t){return gs(e,new he.classes.URLSearchParams,{visitor:function(n,s,r,o){return he.isNode&&b.isBuffer(n)?(this.append(s,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)},...t})}function _d(e){return b.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function wd(e){const t={},n=Object.keys(e);let s;const r=n.length;let o;for(s=0;s<r;s++)o=n[s],t[o]=e[o];return t}function El(e){function t(n,s,r,o){let i=n[o++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),c=o>=n.length;return i=!i&&b.isArray(r)?r.length:i,c?(b.hasOwnProp(r,i)?r[i]=[r[i],s]:r[i]=s,!l):((!r[i]||!b.isObject(r[i]))&&(r[i]=[]),t(n,s,r[i],o)&&b.isArray(r[i])&&(r[i]=wd(r[i])),!l)}if(b.isFormData(e)&&b.isFunction(e.entries)){const n={};return b.forEachEntry(e,(s,r)=>{t(_d(s),r,n,0)}),n}return null}function vd(e,t,n){if(b.isString(e))try{return(t||JSON.parse)(e),b.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const Pr={transitional:xl,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",r=s.indexOf("application/json")>-1,o=b.isObject(t);if(o&&b.isHTMLForm(t)&&(t=new FormData(t)),b.isFormData(t))return r?JSON.stringify(El(t)):t;if(b.isArrayBuffer(t)||b.isBuffer(t)||b.isStream(t)||b.isFile(t)||b.isBlob(t)||b.isReadableStream(t))return t;if(b.isArrayBufferView(t))return t.buffer;if(b.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(o){if(s.indexOf("application/x-www-form-urlencoded")>-1)return yd(t,this.formSerializer).toString();if((l=b.isFileList(t))||s.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return gs(l?{"files[]":t}:t,c&&new c,this.formSerializer)}}return o||r?(n.setContentType("application/json",!1),vd(t)):t}],transformResponse:[function(t){const n=this.transitional||Pr.transitional,s=n&&n.forcedJSONParsing,r=this.responseType==="json";if(b.isResponse(t)||b.isReadableStream(t))return t;if(t&&b.isString(t)&&(s&&!this.responseType||r)){const i=!(n&&n.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?V.from(l,V.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:he.classes.FormData,Blob:he.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};b.forEach(["delete","get","head","post","put","patch"],e=>{Pr.headers[e]={}});const Nr=Pr,xd=b.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ed=e=>{const t={};let n,s,r;return e&&e.split(`
`).forEach(function(i){r=i.indexOf(":"),n=i.substring(0,r).trim().toLowerCase(),s=i.substring(r+1).trim(),!(!n||t[n]&&xd[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},Lo=Symbol("internals");function tn(e){return e&&String(e).trim().toLowerCase()}function Bn(e){return e===!1||e==null?e:b.isArray(e)?e.map(Bn):String(e)}function Sd(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const Rd=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Ls(e,t,n,s,r){if(b.isFunction(s))return s.call(this,t,n);if(r&&(t=n),!!b.isString(t)){if(b.isString(s))return t.indexOf(s)!==-1;if(b.isRegExp(s))return s.test(t)}}function Od(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function Ad(e,t){const n=b.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(r,o,i){return this[s].call(this,t,r,o,i)},configurable:!0})})}class bs{constructor(t){t&&this.set(t)}set(t,n,s){const r=this;function o(l,c,a){const u=tn(c);if(!u)throw new Error("header name must be a non-empty string");const d=b.findKey(r,u);(!d||r[d]===void 0||a===!0||a===void 0&&r[d]!==!1)&&(r[d||c]=Bn(l))}const i=(l,c)=>b.forEach(l,(a,u)=>o(a,u,c));if(b.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(b.isString(t)&&(t=t.trim())&&!Rd(t))i(Ed(t),n);else if(b.isObject(t)&&b.isIterable(t)){let l={},c,a;for(const u of t){if(!b.isArray(u))throw TypeError("Object iterator must return a key-value pair");l[a=u[0]]=(c=l[a])?b.isArray(c)?[...c,u[1]]:[c,u[1]]:u[1]}i(l,n)}else t!=null&&o(n,t,s);return this}get(t,n){if(t=tn(t),t){const s=b.findKey(this,t);if(s){const r=this[s];if(!n)return r;if(n===!0)return Sd(r);if(b.isFunction(n))return n.call(this,r,s);if(b.isRegExp(n))return n.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=tn(t),t){const s=b.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||Ls(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let r=!1;function o(i){if(i=tn(i),i){const l=b.findKey(s,i);l&&(!n||Ls(s,s[l],l,n))&&(delete s[l],r=!0)}}return b.isArray(t)?t.forEach(o):o(t),r}clear(t){const n=Object.keys(this);let s=n.length,r=!1;for(;s--;){const o=n[s];(!t||Ls(this,this[o],o,t,!0))&&(delete this[o],r=!0)}return r}normalize(t){const n=this,s={};return b.forEach(this,(r,o)=>{const i=b.findKey(s,o);if(i){n[i]=Bn(r),delete n[o];return}const l=t?Od(o):String(o).trim();l!==o&&delete n[o],n[l]=Bn(r),s[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return b.forEach(this,(s,r)=>{s!=null&&s!==!1&&(n[r]=t&&b.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(r=>s.set(r)),s}static accessor(t){const s=(this[Lo]=this[Lo]={accessors:{}}).accessors,r=this.prototype;function o(i){const l=tn(i);s[l]||(Ad(r,i),s[l]=!0)}return b.isArray(t)?t.forEach(o):o(t),this}}bs.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);b.reduceDescriptors(bs.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});b.freezeMethods(bs);const Be=bs;function Ms(e,t){const n=this||Nr,s=t||n,r=Be.from(s.headers);let o=s.data;return b.forEach(e,function(l){o=l.call(n,o,r.normalize(),t?t.status:void 0)}),r.normalize(),o}function Sl(e){return!!(e&&e.__CANCEL__)}function Gt(e,t,n){V.call(this,e??"canceled",V.ERR_CANCELED,t,n),this.name="CanceledError"}b.inherits(Gt,V,{__CANCEL__:!0});function Rl(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new V("Request failed with status code "+n.status,[V.ERR_BAD_REQUEST,V.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Td(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Cd(e,t){e=e||10;const n=new Array(e),s=new Array(e);let r=0,o=0,i;return t=t!==void 0?t:1e3,function(c){const a=Date.now(),u=s[o];i||(i=a),n[r]=c,s[r]=a;let d=o,h=0;for(;d!==r;)h+=n[d++],d=d%e;if(r=(r+1)%e,r===o&&(o=(o+1)%e),a-i<t)return;const m=u&&a-u;return m?Math.round(h*1e3/m):void 0}}function Pd(e,t){let n=0,s=1e3/t,r,o;const i=(a,u=Date.now())=>{n=u,r=null,o&&(clearTimeout(o),o=null),e(...a)};return[(...a)=>{const u=Date.now(),d=u-n;d>=s?i(a,u):(r=a,o||(o=setTimeout(()=>{o=null,i(r)},s-d)))},()=>r&&i(r)]}const Xn=(e,t,n=3)=>{let s=0;const r=Cd(50,250);return Pd(o=>{const i=o.loaded,l=o.lengthComputable?o.total:void 0,c=i-s,a=r(c),u=i<=l;s=i;const d={loaded:i,total:l,progress:l?i/l:void 0,bytes:c,rate:a||void 0,estimated:a&&l&&u?(l-i)/a:void 0,event:o,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(d)},n)},Mo=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},Do=e=>(...t)=>b.asap(()=>e(...t)),Nd=he.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,he.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(he.origin),he.navigator&&/(msie|trident)/i.test(he.navigator.userAgent)):()=>!0,Id=he.hasStandardBrowserEnv?{write(e,t,n,s,r,o){const i=[e+"="+encodeURIComponent(t)];b.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),b.isString(s)&&i.push("path="+s),b.isString(r)&&i.push("domain="+r),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Fd(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Ld(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Ol(e,t,n){let s=!Fd(t);return e&&(s||n==!1)?Ld(e,t):t}const Uo=e=>e instanceof Be?{...e}:e;function Nt(e,t){t=t||{};const n={};function s(a,u,d,h){return b.isPlainObject(a)&&b.isPlainObject(u)?b.merge.call({caseless:h},a,u):b.isPlainObject(u)?b.merge({},u):b.isArray(u)?u.slice():u}function r(a,u,d,h){if(b.isUndefined(u)){if(!b.isUndefined(a))return s(void 0,a,d,h)}else return s(a,u,d,h)}function o(a,u){if(!b.isUndefined(u))return s(void 0,u)}function i(a,u){if(b.isUndefined(u)){if(!b.isUndefined(a))return s(void 0,a)}else return s(void 0,u)}function l(a,u,d){if(d in t)return s(a,u);if(d in e)return s(void 0,a)}const c={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(a,u,d)=>r(Uo(a),Uo(u),d,!0)};return b.forEach(Object.keys({...e,...t}),function(u){const d=c[u]||r,h=d(e[u],t[u],u);b.isUndefined(h)&&d!==l||(n[u]=h)}),n}const Al=e=>{const t=Nt({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:r,xsrfCookieName:o,headers:i,auth:l}=t;t.headers=i=Be.from(i),t.url=vl(Ol(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let c;if(b.isFormData(n)){if(he.hasStandardBrowserEnv||he.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((c=i.getContentType())!==!1){const[a,...u]=c?c.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([a||"multipart/form-data",...u].join("; "))}}if(he.hasStandardBrowserEnv&&(s&&b.isFunction(s)&&(s=s(t)),s||s!==!1&&Nd(t.url))){const a=r&&o&&Id.read(o);a&&i.set(r,a)}return t},Md=typeof XMLHttpRequest<"u",Dd=Md&&function(e){return new Promise(function(n,s){const r=Al(e);let o=r.data;const i=Be.from(r.headers).normalize();let{responseType:l,onUploadProgress:c,onDownloadProgress:a}=r,u,d,h,m,y;function v(){m&&m(),y&&y(),r.cancelToken&&r.cancelToken.unsubscribe(u),r.signal&&r.signal.removeEventListener("abort",u)}let S=new XMLHttpRequest;S.open(r.method.toUpperCase(),r.url,!0),S.timeout=r.timeout;function P(){if(!S)return;const F=Be.from("getAllResponseHeaders"in S&&S.getAllResponseHeaders()),q={data:!l||l==="text"||l==="json"?S.responseText:S.response,status:S.status,statusText:S.statusText,headers:F,config:e,request:S};Rl(function(W){n(W),v()},function(W){s(W),v()},q),S=null}"onloadend"in S?S.onloadend=P:S.onreadystatechange=function(){!S||S.readyState!==4||S.status===0&&!(S.responseURL&&S.responseURL.indexOf("file:")===0)||setTimeout(P)},S.onabort=function(){S&&(s(new V("Request aborted",V.ECONNABORTED,e,S)),S=null)},S.onerror=function(){s(new V("Network Error",V.ERR_NETWORK,e,S)),S=null},S.ontimeout=function(){let L=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const q=r.transitional||xl;r.timeoutErrorMessage&&(L=r.timeoutErrorMessage),s(new V(L,q.clarifyTimeoutError?V.ETIMEDOUT:V.ECONNABORTED,e,S)),S=null},o===void 0&&i.setContentType(null),"setRequestHeader"in S&&b.forEach(i.toJSON(),function(L,q){S.setRequestHeader(q,L)}),b.isUndefined(r.withCredentials)||(S.withCredentials=!!r.withCredentials),l&&l!=="json"&&(S.responseType=r.responseType),a&&([h,y]=Xn(a,!0),S.addEventListener("progress",h)),c&&S.upload&&([d,m]=Xn(c),S.upload.addEventListener("progress",d),S.upload.addEventListener("loadend",m)),(r.cancelToken||r.signal)&&(u=F=>{S&&(s(!F||F.type?new Gt(null,e,S):F),S.abort(),S=null)},r.cancelToken&&r.cancelToken.subscribe(u),r.signal&&(r.signal.aborted?u():r.signal.addEventListener("abort",u)));const T=Td(r.url);if(T&&he.protocols.indexOf(T)===-1){s(new V("Unsupported protocol "+T+":",V.ERR_BAD_REQUEST,e));return}S.send(o||null)})},Ud=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,r;const o=function(a){if(!r){r=!0,l();const u=a instanceof Error?a:this.reason;s.abort(u instanceof V?u:new Gt(u instanceof Error?u.message:u))}};let i=t&&setTimeout(()=>{i=null,o(new V(`timeout ${t} of ms exceeded`,V.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(a=>{a.unsubscribe?a.unsubscribe(o):a.removeEventListener("abort",o)}),e=null)};e.forEach(a=>a.addEventListener("abort",o));const{signal:c}=s;return c.unsubscribe=()=>b.asap(l),c}},$d=Ud,kd=function*(e,t){let n=e.byteLength;if(!t||n<t){yield e;return}let s=0,r;for(;s<n;)r=s+t,yield e.slice(s,r),s=r},jd=async function*(e,t){for await(const n of Bd(e))yield*kd(n,t)},Bd=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},$o=(e,t,n,s)=>{const r=jd(e,t);let o=0,i,l=c=>{i||(i=!0,s&&s(c))};return new ReadableStream({async pull(c){try{const{done:a,value:u}=await r.next();if(a){l(),c.close();return}let d=u.byteLength;if(n){let h=o+=d;n(h)}c.enqueue(new Uint8Array(u))}catch(a){throw l(a),a}},cancel(c){return l(c),r.return()}},{highWaterMark:2})},ys=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Tl=ys&&typeof ReadableStream=="function",Hd=ys&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Cl=(e,...t)=>{try{return!!e(...t)}catch{return!1}},qd=Tl&&Cl(()=>{let e=!1;const t=new Request(he.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),ko=64*1024,nr=Tl&&Cl(()=>b.isReadableStream(new Response("").body)),Qn={stream:nr&&(e=>e.body)};ys&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Qn[t]&&(Qn[t]=b.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new V(`Response type '${t}' is not supported`,V.ERR_NOT_SUPPORT,s)})})})(new Response);const Vd=async e=>{if(e==null)return 0;if(b.isBlob(e))return e.size;if(b.isSpecCompliantForm(e))return(await new Request(he.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(b.isArrayBufferView(e)||b.isArrayBuffer(e))return e.byteLength;if(b.isURLSearchParams(e)&&(e=e+""),b.isString(e))return(await Hd(e)).byteLength},Kd=async(e,t)=>{const n=b.toFiniteNumber(e.getContentLength());return n??Vd(t)},Wd=ys&&(async e=>{let{url:t,method:n,data:s,signal:r,cancelToken:o,timeout:i,onDownloadProgress:l,onUploadProgress:c,responseType:a,headers:u,withCredentials:d="same-origin",fetchOptions:h}=Al(e);a=a?(a+"").toLowerCase():"text";let m=$d([r,o&&o.toAbortSignal()],i),y;const v=m&&m.unsubscribe&&(()=>{m.unsubscribe()});let S;try{if(c&&qd&&n!=="get"&&n!=="head"&&(S=await Kd(u,s))!==0){let q=new Request(t,{method:"POST",body:s,duplex:"half"}),te;if(b.isFormData(s)&&(te=q.headers.get("content-type"))&&u.setContentType(te),q.body){const[W,ge]=Mo(S,Xn(Do(c)));s=$o(q.body,ko,W,ge)}}b.isString(d)||(d=d?"include":"omit");const P="credentials"in Request.prototype;y=new Request(t,{...h,signal:m,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:s,duplex:"half",credentials:P?d:void 0});let T=await fetch(y,h);const F=nr&&(a==="stream"||a==="response");if(nr&&(l||F&&v)){const q={};["status","statusText","headers"].forEach(Le=>{q[Le]=T[Le]});const te=b.toFiniteNumber(T.headers.get("content-length")),[W,ge]=l&&Mo(te,Xn(Do(l),!0))||[];T=new Response($o(T.body,ko,W,()=>{ge&&ge(),v&&v()}),q)}a=a||"text";let L=await Qn[b.findKey(Qn,a)||"text"](T,e);return!F&&v&&v(),await new Promise((q,te)=>{Rl(q,te,{data:L,headers:Be.from(T.headers),status:T.status,statusText:T.statusText,config:e,request:y})})}catch(P){throw v&&v(),P&&P.name==="TypeError"&&/Load failed|fetch/i.test(P.message)?Object.assign(new V("Network Error",V.ERR_NETWORK,e,y),{cause:P.cause||P}):V.from(P,P&&P.code,e,y)}}),sr={http:od,xhr:Dd,fetch:Wd};b.forEach(sr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const jo=e=>`- ${e}`,zd=e=>b.isFunction(e)||e===null||e===!1,Pl={getAdapter:e=>{e=b.isArray(e)?e:[e];const{length:t}=e;let n,s;const r={};for(let o=0;o<t;o++){n=e[o];let i;if(s=n,!zd(n)&&(s=sr[(i=String(n)).toLowerCase()],s===void 0))throw new V(`Unknown adapter '${i}'`);if(s)break;r[i||"#"+o]=s}if(!s){const o=Object.entries(r).map(([l,c])=>`adapter ${l} `+(c===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(jo).join(`
`):" "+jo(o[0]):"as no adapter specified";throw new V("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return s},adapters:sr};function Ds(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Gt(null,e)}function Bo(e){return Ds(e),e.headers=Be.from(e.headers),e.data=Ms.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Pl.getAdapter(e.adapter||Nr.adapter)(e).then(function(s){return Ds(e),s.data=Ms.call(e,e.transformResponse,s),s.headers=Be.from(s.headers),s},function(s){return Sl(s)||(Ds(e),s&&s.response&&(s.response.data=Ms.call(e,e.transformResponse,s.response),s.response.headers=Be.from(s.response.headers))),Promise.reject(s)})}const Nl="1.11.0",_s={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{_s[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const Ho={};_s.transitional=function(t,n,s){function r(o,i){return"[Axios v"+Nl+"] Transitional option '"+o+"'"+i+(s?". "+s:"")}return(o,i,l)=>{if(t===!1)throw new V(r(i," has been removed"+(n?" in "+n:"")),V.ERR_DEPRECATED);return n&&!Ho[i]&&(Ho[i]=!0,console.warn(r(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,l):!0}};_s.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function Jd(e,t,n){if(typeof e!="object")throw new V("options must be an object",V.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let r=s.length;for(;r-- >0;){const o=s[r],i=t[o];if(i){const l=e[o],c=l===void 0||i(l,o,e);if(c!==!0)throw new V("option "+o+" must be "+c,V.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new V("Unknown option "+o,V.ERR_BAD_OPTION)}}const Hn={assertOptions:Jd,validators:_s},Ge=Hn.validators;class Yn{constructor(t){this.defaults=t||{},this.interceptors={request:new Fo,response:new Fo}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const o=r.stack?r.stack.replace(/^.+\n/,""):"";try{s.stack?o&&!String(s.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+o):s.stack=o}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=Nt(this.defaults,n);const{transitional:s,paramsSerializer:r,headers:o}=n;s!==void 0&&Hn.assertOptions(s,{silentJSONParsing:Ge.transitional(Ge.boolean),forcedJSONParsing:Ge.transitional(Ge.boolean),clarifyTimeoutError:Ge.transitional(Ge.boolean)},!1),r!=null&&(b.isFunction(r)?n.paramsSerializer={serialize:r}:Hn.assertOptions(r,{encode:Ge.function,serialize:Ge.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),Hn.assertOptions(n,{baseUrl:Ge.spelling("baseURL"),withXsrfToken:Ge.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&b.merge(o.common,o[n.method]);o&&b.forEach(["delete","get","head","post","put","patch","common"],y=>{delete o[y]}),n.headers=Be.concat(i,o);const l=[];let c=!0;this.interceptors.request.forEach(function(v){typeof v.runWhen=="function"&&v.runWhen(n)===!1||(c=c&&v.synchronous,l.unshift(v.fulfilled,v.rejected))});const a=[];this.interceptors.response.forEach(function(v){a.push(v.fulfilled,v.rejected)});let u,d=0,h;if(!c){const y=[Bo.bind(this),void 0];for(y.unshift(...l),y.push(...a),h=y.length,u=Promise.resolve(n);d<h;)u=u.then(y[d++],y[d++]);return u}h=l.length;let m=n;for(d=0;d<h;){const y=l[d++],v=l[d++];try{m=y(m)}catch(S){v.call(this,S);break}}try{u=Bo.call(this,m)}catch(y){return Promise.reject(y)}for(d=0,h=a.length;d<h;)u=u.then(a[d++],a[d++]);return u}getUri(t){t=Nt(this.defaults,t);const n=Ol(t.baseURL,t.url,t.allowAbsoluteUrls);return vl(n,t.params,t.paramsSerializer)}}b.forEach(["delete","get","head","options"],function(t){Yn.prototype[t]=function(n,s){return this.request(Nt(s||{},{method:t,url:n,data:(s||{}).data}))}});b.forEach(["post","put","patch"],function(t){function n(s){return function(o,i,l){return this.request(Nt(l||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}Yn.prototype[t]=n(),Yn.prototype[t+"Form"]=n(!0)});const qn=Yn;class Ir{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const s=this;this.promise.then(r=>{if(!s._listeners)return;let o=s._listeners.length;for(;o-- >0;)s._listeners[o](r);s._listeners=null}),this.promise.then=r=>{let o;const i=new Promise(l=>{s.subscribe(l),o=l}).then(r);return i.cancel=function(){s.unsubscribe(o)},i},t(function(o,i,l){s.reason||(s.reason=new Gt(o,i,l),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Ir(function(r){t=r}),cancel:t}}}const Gd=Ir;function Xd(e){return function(n){return e.apply(null,n)}}function Qd(e){return b.isObject(e)&&e.isAxiosError===!0}const rr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(rr).forEach(([e,t])=>{rr[t]=e});const Yd=rr;function Il(e){const t=new qn(e),n=ul(qn.prototype.request,t);return b.extend(n,qn.prototype,t,{allOwnKeys:!0}),b.extend(n,t,null,{allOwnKeys:!0}),n.create=function(r){return Il(Nt(e,r))},n}const le=Il(Nr);le.Axios=qn;le.CanceledError=Gt;le.CancelToken=Gd;le.isCancel=Sl;le.VERSION=Nl;le.toFormData=gs;le.AxiosError=V;le.Cancel=le.CanceledError;le.all=function(t){return Promise.all(t)};le.spread=Xd;le.isAxiosError=Qd;le.mergeConfig=Nt;le.AxiosHeaders=Be;le.formToJSON=e=>El(b.isHTMLForm(e)?new FormData(e):e);le.getAdapter=Pl.getAdapter;le.HttpStatusCode=Yd;le.default=le;const qo=le,Vo="http://localhost:3000/api";class Zd{async login(t){const n=await qo.post(`${Vo}/users/login`,t),{user:s,token:r}=n.data;return localStorage.setItem("token",r),{user:s,token:r}}async register(t){const n=await qo.post(`${Vo}/users/register`,t),{user:s,token:r}=n.data;return localStorage.setItem("token",r),{user:s,token:r}}logout(){localStorage.removeItem("token")}getCurrentUser(){return this.getToken(),null}getToken(){return localStorage.getItem("token")}isAuthenticated(){return!!this.getToken()}}const Zn=new Zd,ep={class:"max-w-md mx-auto"},tp={class:"bg-white p-8 rounded-lg shadow-md"},np={key:0,class:"bg-red-50 text-red-700 p-3 rounded mb-4"},sp={class:"mb-4"},rp={class:"mb-6"},op=["disabled"],ip={class:"mt-4 text-center"},lp={class:"text-gray-600"},cp=vt({__name:"Login",setup(e){const t=Or(),n=lt({email:"",password:""}),s=lt(null),r=lt(!1),o=l=>{const c=l.target;n.value={...n.value,[c.name]:c.value}},i=async l=>{var c,a;l.preventDefault(),r.value=!0,s.value=null;try{await Zn.login(n.value),t.push("/profile")}catch(u){s.value=((a=(c=u.response)==null?void 0:c.data)==null?void 0:a.error)||"An error occurred during login"}finally{r.value=!1}};return(l,c)=>{const a=Rn("router-link");return Ae(),Ne("div",ep,[U("div",tp,[c[6]||(c[6]=U("h2",{class:"text-2xl font-bold mb-6 text-center"},"Login to Your Account",-1)),s.value?(Ae(),Ne("div",np,yt(s.value),1)):Wi("",!0),U("form",{onSubmit:i},[U("div",sp,[c[2]||(c[2]=U("label",{for:"email",class:"block text-gray-700 mb-2"}," Email Address ",-1)),cn(U("input",{type:"email",id:"email",name:"email","onUpdate:modelValue":c[0]||(c[0]=u=>n.value.email=u),onInput:o,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:""},null,544),[[pn,n.value.email]])]),U("div",rp,[c[3]||(c[3]=U("label",{for:"password",class:"block text-gray-700 mb-2"}," Password ",-1)),cn(U("input",{type:"password",id:"password",name:"password","onUpdate:modelValue":c[1]||(c[1]=u=>n.value.password=u),onInput:o,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:""},null,544),[[pn,n.value.password]])]),U("button",{type:"submit",disabled:r.value,class:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"},yt(r.value?"Logging in...":"Login"),9,op)],32),U("div",ip,[U("p",lp,[c[5]||(c[5]=Ie(" Don't have an account? ",-1)),re(a,{to:"/register",class:"text-blue-600 hover:underline"},{default:Ye(()=>c[4]||(c[4]=[Ie(" Register here ",-1)])),_:1,__:[4]})])])])])}}});const up=zt(cp,[["__scopeId","data-v-a0f1075d"]]),ap={class:"max-w-md mx-auto"},fp={class:"bg-white p-8 rounded-lg shadow-md"},dp={key:0,class:"bg-red-50 text-red-700 p-3 rounded mb-4"},pp={class:"mb-4"},hp={class:"mb-4"},mp={class:"mb-6"},gp=["disabled"],bp={class:"mt-4 text-center"},yp={class:"text-gray-600"},_p=vt({__name:"Register",setup(e){const t=Or(),n=lt({username:"",email:"",password:""}),s=lt(null),r=lt(!1),o=l=>{const c=l.target;n.value={...n.value,[c.name]:c.value}},i=async l=>{var c,a;l.preventDefault(),r.value=!0,s.value=null;try{await Zn.register(n.value),t.push("/profile")}catch(u){s.value=((a=(c=u.response)==null?void 0:c.data)==null?void 0:a.error)||"An error occurred during registration"}finally{r.value=!1}};return(l,c)=>{const a=Rn("router-link");return Ae(),Ne("div",ap,[U("div",fp,[c[8]||(c[8]=U("h2",{class:"text-2xl font-bold mb-6 text-center"},"Create an Account",-1)),s.value?(Ae(),Ne("div",dp,yt(s.value),1)):Wi("",!0),U("form",{onSubmit:i},[U("div",pp,[c[3]||(c[3]=U("label",{for:"username",class:"block text-gray-700 mb-2"}," Username ",-1)),cn(U("input",{type:"text",id:"username",name:"username","onUpdate:modelValue":c[0]||(c[0]=u=>n.value.username=u),onInput:o,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:""},null,544),[[pn,n.value.username]])]),U("div",hp,[c[4]||(c[4]=U("label",{for:"email",class:"block text-gray-700 mb-2"}," Email Address ",-1)),cn(U("input",{type:"email",id:"email",name:"email","onUpdate:modelValue":c[1]||(c[1]=u=>n.value.email=u),onInput:o,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:""},null,544),[[pn,n.value.email]])]),U("div",mp,[c[5]||(c[5]=U("label",{for:"password",class:"block text-gray-700 mb-2"}," Password ",-1)),cn(U("input",{type:"password",id:"password",name:"password","onUpdate:modelValue":c[2]||(c[2]=u=>n.value.password=u),onInput:o,class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",required:""},null,544),[[pn,n.value.password]])]),U("button",{type:"submit",disabled:r.value,class:"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"},yt(r.value?"Creating Account...":"Register"),9,gp)],32),U("div",bp,[U("p",yp,[c[7]||(c[7]=Ie(" Already have an account? ",-1)),re(a,{to:"/login",class:"text-blue-600 hover:underline"},{default:Ye(()=>c[6]||(c[6]=[Ie(" Login here ",-1)])),_:1,__:[6]})])])])])}}});const wp=zt(_p,[["__scopeId","data-v-fc80b5f4"]]),vp={class:"max-w-2xl mx-auto"},xp={class:"bg-white p-8 rounded-lg shadow-md"},Ep={key:0,class:"text-center"},Sp={key:1},Rp={key:0},Op={class:"mb-6"},Ap={class:"bg-gray-50 p-4 rounded-lg"},Tp={class:"grid grid-cols-2 gap-4"},Cp={class:"font-medium"},Pp={class:"font-medium"},Np={class:"font-medium"},Ip={key:1,class:"text-center py-8"},Fp=vt({__name:"Profile",setup(e){const t=Or(),n=lt(null),s=lt(!0),r=async()=>{try{const i=Zn.getCurrentUser();n.value={id:1,username:"admin",email:"<EMAIL>"}}catch(i){console.error("Failed to fetch user profile",i)}finally{s.value=!1}},o=()=>{Zn.logout(),t.push("/login")};return Ri(()=>{r()}),(i,l)=>(Ae(),Ne("div",vp,[U("div",xp,[l[7]||(l[7]=U("h2",{class:"text-2xl font-bold mb-6"},"User Profile",-1)),s.value?(Ae(),Ne("div",Ep,l[1]||(l[1]=[U("p",null,"Loading profile...",-1)]))):(Ae(),Ne("div",Sp,[n.value?(Ae(),Ne("div",Rp,[U("div",Op,[l[5]||(l[5]=U("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Account Information",-1)),U("div",Ap,[U("div",Tp,[U("div",null,[l[2]||(l[2]=U("p",{class:"text-sm text-gray-500"},"Username",-1)),U("p",Cp,yt(n.value.username),1)]),U("div",null,[l[3]||(l[3]=U("p",{class:"text-sm text-gray-500"},"Email",-1)),U("p",Pp,yt(n.value.email),1)]),U("div",null,[l[4]||(l[4]=U("p",{class:"text-sm text-gray-500"},"User ID",-1)),U("p",Np,yt(n.value.id),1)])])])]),U("div",{class:"flex gap-3"},[U("button",{onClick:o,class:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"}," Logout ")])])):(Ae(),Ne("div",Ip,[l[6]||(l[6]=U("p",{class:"text-gray-600 mb-4"},"You are not logged in.",-1)),U("button",{onClick:l[0]||(l[0]=c=>Ct(t).push("/login")),class:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"}," Go to Login ")]))]))])]))}});const Lp=zt(Fp,[["__scopeId","data-v-07d94520"]]),Mp=[{path:"/",name:"Home",component:_f},{path:"/login",name:"Login",component:up},{path:"/register",name:"Register",component:wp},{path:"/profile",name:"Profile",component:Lp}],Dp=ef({history:Ca(),routes:Mp});const Fl=Xu(hf);Fl.use(Dp);Fl.mount("#app");
