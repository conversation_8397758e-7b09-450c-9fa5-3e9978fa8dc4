<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import authService from '../services/authService'

interface User {
  id: number
  username: string
  email: string
}

const router = useRouter()
const user = ref<User | null>(null)
const loading = ref(true)

const fetchUserProfile = async () => {
  try {
    const token = authService.getToken()
    if (!token) {
      router.push('/login')
      return
    }

    // Fetch user profile from the API
    const response = await fetch('http://localhost:3000/api/users/profile', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const userData = await response.json()
      user.value = {
        id: userData.id,
        username: userData.username,
        email: userData.email
      }
    } else {
      // Handle error response
      console.error('Failed to fetch user profile')
      authService.logout()
      router.push('/login')
    }
  } catch (err) {
    console.error('Failed to fetch user profile', err)
    authService.logout()
    router.push('/login')
  } finally {
    loading.value = false
  }
}

const handleLogout = () => {
  authService.logout()
  router.push('/login')
}

onMounted(() => {
  fetchUserProfile()
})
</script>

<template>
  <div class="max-w-2xl mx-auto">
    <div class="bg-white p-8 rounded-lg shadow-md">
      <h2 class="text-2xl font-bold mb-6">User Profile</h2>
      
      <div v-if="loading" class="text-center">
        <p>Loading profile...</p>
      </div>
      
      <div v-else>
        <div v-if="user">
          <div class="mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-2">Account Information</h3>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <p class="text-sm text-gray-500">Username</p>
                  <p class="font-medium">{{ user.username }}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">Email</p>
                  <p class="font-medium">{{ user.email }}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">User ID</p>
                  <p class="font-medium">{{ user.id }}</p>
                </div>
              </div>
            </div>
          </div>
          
          <div class="flex gap-3">
            <button
              @click="handleLogout"
              class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Logout
            </button>
          </div>
        </div>
        
        <div v-else class="text-center py-8">
          <p class="text-gray-600 mb-4">You are not logged in.</p>
          <button
            @click="router.push('/login')"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            Go to Login
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

