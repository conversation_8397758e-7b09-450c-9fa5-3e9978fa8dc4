<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import authService from '../services/authService'

interface RegisterFormData {
  username: string
  email: string
  password: string
}

const router = useRouter()
const formData = ref<RegisterFormData>({
  username: '',
  email: '',
  password: ''
})
const error = ref<string | null>(null)
const loading = ref(false)

const handleChange = (e: Event) => {
  const target = e.target as HTMLInputElement
  formData.value = {
    ...formData.value,
    [target.name]: target.value
  }
}

const handleSubmit = async (e: Event) => {
  e.preventDefault()
  loading.value = true
  error.value = null

  try {
    await authService.register(formData.value)
    router.push('/profile')
  } catch (err: any) {
    error.value = err.response?.data?.error || 'An error occurred during registration'
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="max-w-md mx-auto">
    <div class="bg-white p-8 rounded-lg shadow-md">
      <h2 class="text-2xl font-bold mb-6 text-center">Create an Account</h2>
      
      <div v-if="error" class="bg-red-50 text-red-700 p-3 rounded mb-4">
        {{ error }}
      </div>
      
      <form @submit="handleSubmit">
        <div class="mb-4">
          <label for="username" class="block text-gray-700 mb-2">
            Username
          </label>
          <input
            type="text"
            id="username"
            name="username"
            v-model="formData.username"
            @input="handleChange"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>
        
        <div class="mb-4">
          <label for="email" class="block text-gray-700 mb-2">
            Email Address
          </label>
          <input
            type="email"
            id="email"
            name="email"
            v-model="formData.email"
            @input="handleChange"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>
        
        <div class="mb-6">
          <label for="password" class="block text-gray-700 mb-2">
            Password
          </label>
          <input
            type="password"
            id="password"
            name="password"
            v-model="formData.password"
            @input="handleChange"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            required
          />
        </div>
        
        <button
          type="submit"
          :disabled="loading"
          class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          {{ loading ? 'Creating Account...' : 'Register' }}
        </button>
      </form>
      
      <div class="mt-4 text-center">
        <p class="text-gray-600">
          Already have an account? 
          <router-link to="/login" class="text-blue-600 hover:underline">
            Login here
          </router-link>
        </p>
      </div>
    </div>
  </div>
</template>

