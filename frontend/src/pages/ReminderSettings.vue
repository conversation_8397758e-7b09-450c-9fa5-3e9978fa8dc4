<template>
  <div class="max-w-2xl mx-auto">
    <div class="bg-white p-8 rounded-lg shadow-md">
      <h2 class="text-2xl font-bold mb-6">Reminder Settings</h2>
      
      <div v-if="loading" class="text-center">
        <p>Loading settings...</p>
      </div>
      
      <div v-else>
        <form @submit="updateReminderTime">
          <div class="mb-6">
            <label for="reminder-time" class="block text-gray-700 mb-2">
              Reminder Time
            </label>
            <input
              type="time"
              id="reminder-time"
              v-model="reminderTime"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p class="mt-2 text-sm text-gray-500">
              Set the time you want to receive holiday reminders (daily at this time)
            </p>
          </div>
          
          <div v-if="successMessage" class="mb-4 p-3 bg-green-100 text-green-700 rounded">
            {{ successMessage }}
          </div>
          
          <div v-if="errorMessage" class="mb-4 p-3 bg-red-100 text-red-700 rounded">
            {{ errorMessage }}
          </div>
          
          <button
            type="submit"
            :disabled="saving"
            class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {{ saving ? 'Saving...' : 'Save Settings' }}
          </button>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import authService from '../services/authService'

const router = useRouter()
const loading = ref(true)
const saving = ref(false)
const reminderTime = ref('')
const successMessage = ref('')
const errorMessage = ref('')

const fetchReminderTime = async () => {
  try {
    const token = authService.getToken()
    if (!token) {
      router.push('/login')
      return
    }

    // Get user ID from token or profile
    const response = await fetch('http://localhost:3000/api/users/profile', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const userData = await response.json()
      const userId = userData.id

      // Fetch current reminder time
      const reminderResponse = await fetch(`http://localhost:3000/api/user-settings/${userId}/reminder-time`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (reminderResponse.ok) {
        const time = await reminderResponse.json()
        if (time) {
          // Format time for input element (HH:MM)
          const hours = time.hour.toString().padStart(2, '0')
          const minutes = time.minute.toString().padStart(2, '0')
          reminderTime.value = `${hours}:${minutes}`
        }
      }
    } else {
      authService.logout()
      router.push('/login')
    }
  } catch (err) {
    console.error('Failed to fetch reminder time', err)
    errorMessage.value = 'Failed to load settings'
  } finally {
    loading.value = false
  }
}

const updateReminderTime = async (e: Event) => {
  e.preventDefault()
  saving.value = true
  successMessage.value = ''
  errorMessage.value = ''

  try {
    const token = authService.getToken()
    if (!token) {
      router.push('/login')
      return
    }

    // Get user ID from profile
    const response = await fetch('http://localhost:3000/api/users/profile', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (response.ok) {
      const userData = await response.json()
      const userId = userData.id

      // Parse time from input (HH:MM format)
      const [hours, minutes] = reminderTime.value.split(':').map(Number)
      
      // Update reminder time
      const updateResponse = await fetch(`http://localhost:3000/api/user-settings/${userId}/reminder-time`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          hour: hours,
          minute: minutes
        })
      })

      if (updateResponse.ok) {
        successMessage.value = 'Reminder time updated successfully!'
      } else {
        errorMessage.value = 'Failed to update reminder time'
      }
    } else {
      authService.logout()
      router.push('/login')
    }
  } catch (err) {
    console.error('Failed to update reminder time', err)
    errorMessage.value = 'Failed to update reminder time'
  } finally {
    saving.value = false
  }
}

onMounted(() => {
  fetchReminderTime()
})
</script>

