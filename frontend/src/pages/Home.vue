<script setup lang="ts">
// No script needed for this component
</script>

<template>
  <div class="max-w-4xl mx-auto">
    <div class="text-center py-12">
      <h1 class="text-4xl font-bold text-gray-900 mb-4">
        Welcome to MyProject
      </h1>
      <p class="text-xl text-gray-600 mb-8">
        A modern web application built with cutting-edge technologies
      </p>
      
      <div class="flex justify-center gap-4">
        <router-link
          to="/login"
          class="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
        >
          Login
        </router-link>
        <router-link
          to="/register"
          class="px-6 py-3 bg-white text-blue-600 font-medium rounded-lg border border-blue-600 hover:bg-blue-50 transition-colors"
        >
          Register
        </router-link>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16">
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-xl font-semibold mb-2">Modern Architecture</h3>
        <p class="text-gray-600">
          Built with a scalable architecture that supports microservices and cloud deployment.
        </p>
      </div>
      
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-xl font-semibold mb-2">Secure Authentication</h3>
        <p class="text-gray-600">
          Robust authentication system with JWT tokens and secure password handling.
        </p>
      </div>
      
      <div class="bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-xl font-semibold mb-2">Responsive Design</h3>
        <p class="text-gray-600">
          Fully responsive interface that works on all devices from mobile to desktop.
        </p>
      </div>
    </div>
  </div>
</template>

