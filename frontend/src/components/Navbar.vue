<script setup lang="ts">
import { useRoute } from 'vue-router'

const route = useRoute()

const isActive = (path: string) => {
  return route.path === path
}
</script>

<template>
  <nav class="bg-white shadow-md">
    <div class="container mx-auto px-4">
      <div class="flex justify-between items-center h-16">
        <div class="flex items-center">
          <router-link to="/" class="text-xl font-bold text-blue-600">
            MyProject
          </router-link>
        </div>
        
        <div class="flex space-x-8">
          <router-link
            to="/"
            :class="[
              isActive('/') 
                ? 'text-blue-600 border-b-2 border-blue-600' 
                : 'text-gray-600 hover:text-blue-500',
              'px-1 py-2 transition-colors'
            ]"
          >
            Home
          </router-link>
          
          <router-link
            to="/login"
            :class="[
              isActive('/login') 
                ? 'text-blue-600 border-b-2 border-blue-600' 
                : 'text-gray-600 hover:text-blue-500',
              'px-1 py-2 transition-colors'
            ]"
          >
            Login
          </router-link>
          
          <router-link
            to="/register"
            :class="[
              isActive('/register') 
                ? 'text-blue-600 border-b-2 border-blue-600' 
                : 'text-gray-600 hover:text-blue-500',
              'px-1 py-2 transition-colors'
            ]"
          >
            Register
          </router-link>
          
          <router-link
            to="/profile"
            :class="[
              isActive('/profile') 
                ? 'text-blue-600 border-b-2 border-blue-600' 
                : 'text-gray-600 hover:text-blue-500',
              'px-1 py-2 transition-colors'
            ]"
          >
            Profile
          </router-link>
          
          <router-link
            to="/reminder-settings"
            :class="[
              isActive('/reminder-settings')
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-blue-500',
              'px-1 py-2 transition-colors'
            ]"
          >
            Reminder Settings
          </router-link>
        </div>
      </div>
    </div>
  </nav>
</template>

