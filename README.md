# 传统节日问候助手 (Traditional Festival Greeting Assistant)

这是一个现代化的Web应用程序，帮助用户在重要节日向亲朋好友发送问候邮件。系统采用前后端分离架构，提供完整的用户管理、节日提醒、好友管理和邮件发送功能。

## 项目结构

```
.
├── frontend/           # 前端应用 (Vue 3 + TypeScript)
├── backend/            # 后端服务 (Spring Boot + Java 17)
├── database/           # 数据库脚本和迁移
├── docs/               # 完整项目文档
│   ├── architecture/   # 系统架构设计
│   ├── database/      # 数据库设计规范
│   ├── api/           # API接口规范
│   ├── development/  # 开发规范和流程
│   ├── testing/       # 测试策略和质量保证
│   └── deployment/    # 部署和运维规范
├── nginx/             # Nginx配置
├── scripts/           # 部署脚本
└── README.md          # 项目说明文件
```

## 技术架构概览

### 前端
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router
- **UI库**: Tailwind CSS
- **HTTP客户端**: Axios

### 后端
- **框架**: Spring Boot 3.x (Java 17)
- **安全**: Spring Security + JWT
- **数据访问**: Spring Data JPA
- **数据库**: MySQL 8.0
- **缓存**: Redis
- **邮件**: Spring Mail (Gmail SMTP)

### 数据库
- **主数据库**: MySQL 8.0 (用户数据、节日信息、好友信息等)
- **缓存**: Redis (会话管理、临时数据缓存)

### 第三方服务
- **邮件服务**: Gmail SMTP (默认配置，可替换为其他SMTP服务)
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx

## 快速开始

### 环境要求
- Java 17 或更高版本
- Node.js 18.x LTS 或更高版本
- Docker 和 Docker Compose (推荐使用容器化部署)
- Maven 3.6 或更高版本 (如果不使用Docker)

### 使用Docker运行 (推荐)

1. 复制环境变量模板文件并配置:
```bash
cp .env.example .env
# 编辑 .env 文件，填入实际的配置值
```

2. 构建并启动所有服务:
```bash
docker-compose up --build
```

2. 访问应用:
- 前端: http://localhost
- 后端API: http://localhost:3000
- 数据库: localhost:3306
- Redis: localhost:6379

### 本地运行

#### 后端设置:
1. 进入后端目录:
```bash
cd backend
```

2. 使用Maven构建应用:
```bash
mvn clean package
```

3. 启动应用:
```bash
java -jar target/myproject-backend-1.0.0.jar
```

#### 前端设置:
1. 进入前端目录:
```bash
cd frontend
```

2. 安装依赖:
```bash
npm install
```

3. 启动开发服务器:
```bash
npm run dev
```

## API端点

### 认证相关
- `POST /api/auth/signup` - 用户注册
- `POST /api/auth/signin` - 用户登录
- `POST /api/auth/refresh` - 刷新令牌

### 用户管理
- `GET /api/users/me` - 获取当前用户信息
- `PUT /api/users/me` - 更新用户信息
- `PUT /api/users/me/reminder-time` - 更新提醒时间
- `PUT /api/users/me/password` - 修改密码

### 节日管理
- `GET /api/holidays` - 获取节日列表
- `GET /api/holidays/{id}` - 获取特定节日详情

### 好友管理
- `GET /api/friends` - 获取好友列表
- `POST /api/friends` - 添加好友
- `PUT /api/friends/{id}` - 更新好友信息
- `DELETE /api/friends/{id}` - 删除好友
- `PUT /api/friends/{id}/favorite` - 收藏/取消收藏好友

### 好友分组
- `GET /api/friend-groups` - 获取分组列表
- `POST /api/friend-groups` - 创建分组
- `PUT /api/friend-groups/{id}` - 更新分组
- `DELETE /api/friend-groups/{id}` - 删除分组

### 问候模板
- `GET /api/templates` - 获取模板列表
- `POST /api/templates` - 创建模板
- `PUT /api/templates/{id}` - 更新模板
- `DELETE /api/templates/{id}` - 删除模板

### 问候设置
- `GET /api/greetings` - 获取问候设置列表
- `POST /api/greetings` - 创建问候设置
- `PUT /api/greetings/{id}` - 更新问候设置
- `DELETE /api/greetings/{id}` - 删除问候设置

### 邮件记录
- `GET /api/email-logs` - 获取邮件发送记录

## 邮件服务

应用包含自动化的邮件服务:
- 用户注册确认邮件
- 节日问候邮件发送
- 邮件发送状态跟踪
- 发送失败重试机制

## 环境变量配置

项目使用环境变量进行配置管理。请按以下步骤配置：

1. 复制环境变量模板文件:
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入实际的配置值:
   - 数据库连接信息
   - Redis连接信息
   - 邮件服务配置
   - JWT密钥等

3. 重要配置项说明:
   - `SPRING_DATASOURCE_PASSWORD`: 数据库密码
   - `SPRING_MAIL_USERNAME`: 邮件服务用户名
   - `SPRING_MAIL_PASSWORD`: 邮件服务密码（建议使用应用专用密码）
   - `JWT_SECRET`: JWT签名密钥（生产环境请使用强密钥）

### 邮件配置

应用包含邮件服务用于用户注册和节日问候。配置邮件服务:

1. 更新 `backend/src/main/resources/application.yml` 中的邮件设置:
   ```yaml
   spring:
     mail:
       host: smtp.gmail.com
       port: 587
       username: <EMAIL>
       password: your-app-password
       properties:
         mail:
           smtp:
             auth: true
           starttls:
             enable: true
   ```

2. 对于Gmail，您需要:
   - 启用两步验证
   - 生成应用专用密码
   - 使用应用专用密码替代常规密码

3. 支持的邮件提供商:
   - Gmail (默认配置)
   - Outlook/Hotmail
   - Yahoo
   - 自定义SMTP服务器

## 生产环境部署

### 使用Docker Compose进行生产部署

对于生产环境部署，请使用生产环境的docker-compose文件:

1. 从 `.env.prod` 创建 `.env` 文件并更新配置值:
```bash
cp .env.prod .env
# 编辑 .env 文件填入实际值
```

2. 构建并启动所有服务:
```bash
docker-compose -f docker-compose.prod.yml up --build -d
```

3. 访问应用:
- 前端: http://localhost
- 后端API: http://localhost:3000
- 数据库: localhost:3306
- Redis: localhost:6379

### 环境变量

应用使用环境变量进行配置。请参考 `.env.prod` 文件了解所有必需的环境变量。

### Kubernetes部署

对于Kubernetes部署，应用可以使用以下方式部署:
- Helm charts
- Kustomize
- 手动YAML清单

### 云平台部署

应用也可以部署到云平台:
- AWS (ECS, EKS, Beanstalk)
- Google Cloud (GKE, Cloud Run)
- Azure (AKS, Container Instances)

## 完整文档

有关完整的架构设计、数据库设计、API规范、开发流程、测试策略和部署规范，请参阅 `docs/` 目录中的详细文档。

## 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件了解更多详情。

## 联系方式

如有任何问题或建议，请联系项目维护团队。